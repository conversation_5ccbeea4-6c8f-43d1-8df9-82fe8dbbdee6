# Dependabot configuration for WordPress project
version: 2
updates:
  # PHP dependencies (Composer)
  - package-ecosystem: "composer"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "Asia/Manila"
    open-pull-requests-limit: 5
    labels:
      - "dependencies"
      - "php"
      - "security"
    commit-message:
      prefix: "composer"
      include: "scope"
    ignore:
      - dependency-name: "wordpress/*"
        update-types: ["version-update:semver-major"]
      - dependency-name: "wp-*"
        update-types: ["version-update:semver-major"]

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "Asia/Manila"
    open-pull-requests-limit: 3
    labels:
      - "dependencies"
      - "github-actions"
      - "security"
    commit-message:
      prefix: "github-actions"
      include: "scope"
