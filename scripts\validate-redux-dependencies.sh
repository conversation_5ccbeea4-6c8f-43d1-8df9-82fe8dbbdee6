#!/bin/bash

# Redux Framework Dependencies Validation Script
# This script validates that all required Redux Framework files are tracked in Git

echo "🔍 Validating Redux Framework Dependencies..."

# Check if Redux directory exists
if [ ! -d "wp-content/uploads/redux" ]; then
    echo "❌ ERROR: wp-content/uploads/redux directory not found!"
    echo "   Redux Framework requires this directory to function properly."
    exit 1
fi

# Expected Redux files
EXPECTED_FILES=(
    "wp-content/uploads/redux/checkbox.php"
    "wp-content/uploads/redux/color.php"
    "wp-content/uploads/redux/custom-fonts/custom/index.php"
    "wp-content/uploads/redux/custom-fonts/index.php"
    "wp-content/uploads/redux/hash"
    "wp-content/uploads/redux/index.php"
    "wp-content/uploads/redux/media.php"
    "wp-content/uploads/redux/select.php"
    "wp-content/uploads/redux/text.php"
    "wp-content/uploads/redux/textarea.php"
    "wp-content/uploads/redux/version"
)

# Check if all expected files are tracked in Git
MISSING_FILES=()
for file in "${EXPECTED_FILES[@]}"; do
    if ! git ls-files --error-unmatch "$file" >/dev/null 2>&1; then
        MISSING_FILES+=("$file")
    fi
done

# Report results
if [ ${#MISSING_FILES[@]} -eq 0 ]; then
    echo "✅ All 11 Redux Framework files are tracked in Git"
else
    echo "❌ ERROR: Missing Redux Framework files in Git tracking:"
    for file in "${MISSING_FILES[@]}"; do
        echo "   - $file"
    done
    echo ""
    echo "To fix this, run:"
    echo "   git add wp-content/uploads/redux/"
    echo "   git commit -m 'feat: add missing Redux Framework files'"
    exit 1
fi

# Check .gitignore exceptions
echo "🔍 Checking .gitignore exceptions..."
if grep -q "!wp-content/uploads/redux/" .gitignore && \
   grep -q "!wp-content/uploads/redux/\*\.php" .gitignore && \
   grep -q "!wp-content/uploads/redux/hash" .gitignore && \
   grep -q "!wp-content/uploads/redux/version" .gitignore && \
   grep -q "!wp-content/uploads/redux/custom-fonts/" .gitignore; then
    echo "✅ All required .gitignore exceptions are present"
else
    echo "❌ ERROR: Missing .gitignore exceptions for Redux Framework"
    echo "   Required exceptions:"
    echo "   - !wp-content/uploads/redux/"
    echo "   - !wp-content/uploads/redux/*.php"
    echo "   - !wp-content/uploads/redux/hash"
    echo "   - !wp-content/uploads/redux/version"
    echo "   - !wp-content/uploads/redux/custom-fonts/"
    echo ""
    echo "Add these exceptions to .gitignore after the 'wp-content/uploads/' line"
    exit 1
fi

# Check Let's Talk Section implementation files
echo "🔍 Checking Let's Talk Section implementation files..."
IMPLEMENTATION_FILES=(
    "wp-content/plugins/acens-common/redux-framework/sample/sample-config.php"
    "wp-content/themes/trds-child/functions.php"
    "wp-content/themes/acens/single-services.php"
    "wp-content/themes/acens/single.php"
    "wp-content/themes/trds-child/style.css"
)

MISSING_IMPL_FILES=()
for file in "${IMPLEMENTATION_FILES[@]}"; do
    if ! git ls-files --error-unmatch "$file" >/dev/null 2>&1; then
        MISSING_IMPL_FILES+=("$file")
    fi
done

if [ ${#MISSING_IMPL_FILES[@]} -eq 0 ]; then
    echo "✅ All Let's Talk Section implementation files are tracked"
else
    echo "❌ ERROR: Missing Let's Talk Section implementation files:"
    for file in "${MISSING_IMPL_FILES[@]}"; do
        echo "   - $file"
    done
    exit 1
fi

echo ""
echo "🎉 All Redux Framework dependencies are properly configured!"
echo "   - 11 Redux Framework files tracked in Git"
echo "   - .gitignore exceptions configured"
echo "   - Let's Talk Section implementation files present"
echo ""
echo "✅ Repository is ready for deployment"
