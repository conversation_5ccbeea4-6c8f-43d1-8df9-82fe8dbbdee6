# seraphinite-accelerator - Automatic redirection to Avif and WebP versions if they exist
location ~ .*\.(jpe|jpg|jpeg|png|gif|bmp|webp|avif)$
{
	types
	{
		image/jpeg	jpe;
		image/jpeg	jpg;
		image/jpeg	jpeg;
		image/png	png;
		image/gif	gif;
		image/bmp	bmp;
	}

	set $any_redir "";
	set $file_ext "$1";

	set $file_ai "";
	if ($arg_seraph_accel_ai ~* ^([\w\-]+)\.([\w\-]+)$) { set $arg_seraph_accel_ai ""; set $ai_s "$1"; set $ai_f "$2"; set $file_ai "D:/LocalWP/trds-devlive/app/public/wp-content/cache/seraphinite-accelerator/s/${ai_s}/ai/${ai_f}.${file_ext}"; }
	if (-f $file_ai) { rewrite . /wp-content/cache/seraphinite-accelerator/s/${ai_s}/ai/${ai_f}.${file_ext}; set $file_ai ""; }
	if ($file_ai) { rewrite ^(.*)$ /?seraph_accel_gi=$1&ai=${ai_s}.${ai_f}&intrnl=${ai_s} last; break; }

	# webp
	types { image/webp webp; }
	set $webp_redir "";
	if ($http_accept ~* "image\/webp") { set $webp_redir "${webp_redir}A"; }
	if (-f $request_filename.webp) { set $webp_redir "${webp_redir}F"; set $any_redir "R"; }
	if ($webp_redir = "AF") { add_header Vary Accept; rewrite (.*) $1.webp break; }

	if ($any_redir = "") { add_header Vary Accept; }
}
