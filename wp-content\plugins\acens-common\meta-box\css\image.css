.rwmb-image-wrapper .rwmb-uploaded:empty {
	display: none;
}
.rwmb-image-wrapper .rwmb-uploaded:not(:empty) {
	display: block;
	margin: -8px 0 0 -8px;
	overflow: hidden;
}
/* Re-add WP core UI style to make sure frontend works */
.rwmb-image-item {
	position: relative;
	float: left;
	padding: 8px;
	margin: 0;
	color: #444;
	cursor: pointer;
	list-style: none;
	text-align: center;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	width: 25%;
	box-sizing: border-box;
}
.rwmb-image-item.thumbnail {
	width: 150px;
}
.rwmb-image-item.medium {
	width: 300px;
}
.rwmb-image-item.large {
	width: 1024px;
}

.rwmb-image-actions {
	position: absolute;
	z-index: 2;
	right: 12px;
	top: 12px;
	opacity: 0;
	transition: opacity .2s;
	color: #fff;
}
.rwmb-image-edit,
.rwmb-image-delete {
	color: inherit;
	text-decoration: none;
}
.rwmb-image-overlay {
	position: absolute;
	z-index: 1;
	top: 8px;
	bottom: 8px;
	left: 8px;
	right: 8px;
	background: #000;
	opacity: 0;
	transition: opacity .2s;
}
.rwmb-image-item:hover .rwmb-image-actions {
	opacity: 1;
}
.rwmb-image-item:hover .rwmb-image-overlay {
	opacity: .6;
}
