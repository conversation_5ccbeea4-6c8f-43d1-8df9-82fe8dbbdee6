.rwmb-map-canvas {
	width: 100%;
	height: 400px;
	margin-bottom: 10px;
}

/* Autocomplete style, copy from WordPress's common.css and forms.css */

input.ui-autocomplete-input.open {
	border-bottom-color: transparent;
}

.ui-autocomplete {
	padding: 0;
	margin: 0;
	list-style: none;
	position: absolute;
	z-index: 10000;
	border: 1px solid #5b9dd9;
	box-shadow: 0 1px 2px rgba( 30, 140, 190, 0.8 );
	background-color: #fff;
}

.ui-autocomplete li {
	margin-bottom: 0;
	padding: 4px 10px;
	white-space: nowrap;
	text-align: left;
	cursor: pointer;
}

/* Colors for the wplink toolbar autocomplete. */
.ui-autocomplete .ui-state-focus {
	background-color: #ddd;
}

.ui-helper-hidden-accessible {
	border: 0;
	clip: rect(1px, 1px, 1px, 1px);
	-webkit-clip-path: inset(50%);
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
	word-wrap: normal !important; /* many screen reader and browser combinations announce broken words as they would appear visually */
}
