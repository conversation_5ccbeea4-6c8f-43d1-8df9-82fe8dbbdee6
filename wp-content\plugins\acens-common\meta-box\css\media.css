.rwmb-media-list:empty {
	display: none;
}
.rwmb-media-list:not(:empty) {
	display: block;
	margin: -8px 0 0 -8px;
	overflow: hidden;
}
.rwmb-media-item.attachment {
	float: none;
	text-align: left;
	width: auto;
	overflow: hidden;
}
.rwmb-media-preview {
	width: 60px;
	float: left;
	position: relative;
}
.rwmb-media-info {
	margin-left: 65px;
}
.rwmb-media-title {
	font-weight: bold;
	text-decoration: none;
}
.rwmb-media-name {
	margin: 0 0 4px;
}
.rwmb-media-actions {
	margin: 0;
}
.rwmb-edit-media,
.rwmb-remove-media {
	font-size: 11px;
	color: inherit;
	text-decoration: none;
}
.rwmb-media-actions .dashicons {
	font-size: 1em;
	width: 1em;
	height: 1em;
	vertical-align: middle;
}

/* Add more button */
.rwmb-media-view .rwmb-add-media {
	padding-left: 5px;
	margin-right: 5px;
}
