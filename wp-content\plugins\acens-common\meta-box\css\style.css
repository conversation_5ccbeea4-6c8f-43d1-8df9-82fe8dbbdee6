/* Styles for 'normal' meta boxes
-------------------------------------------------------------- */

/* Clearfix for field */
.rwmb-field:after {
	content: " ";
	display: table;
	clear: both;
}
.rwmb-field:not(:last-of-type) {
	margin: 0 0 12px;
}
.rwmb-label,
.rwmb-input {
	vertical-align: top;
	float: left;
	box-sizing: border-box;
}
.rwmb-label {
	width: 25%;
}
.rwmb-label > label {
	font-weight: 600;
}
.rwmb-required {
	color: #dc3232;
	font-weight: bold;
	margin-left: 3px;
}
.rwmb-input input,
.rwmb-input select {
	max-width: 99%;
}

/* 75% if field has label, 100% if no label */
.rwmb-input {
	width: 100%;
}
.rwmb-label ~ .rwmb-input {
	width: 75%;
}
.rwmb-input h4 {
	margin: 0;
}
.rwmb-textarea {
	resize: vertical;
}

/* Clone */
.rwmb-clone {
	min-height: 24px;
	margin-bottom: 12px;
	position: relative;
	clear: both;
	background: #fff;
}
.rwmb-clone > input[type='radio'],
.rwmb-clone > input[type='checkbox'] {
	margin: 6px 0 0 4px;
}
.rwmb-button.remove-clone {
	text-decoration: none;
	color: #ccc;
	display: inline-block;
	position: absolute;
	top: 0;
	right: 0;
	width: 20px;
	height: 20px;
	transition: color 200ms;
}
.rwmb-button.remove-clone .dashicons {
	font-size: 20px;
}
.rwmb-button.remove-clone:hover {
	color: #dc3232;
}
.remove-clone:focus {
	outline: 0;
	box-shadow: none;
}
.rwmb-button.add-clone {
	margin-top: 5px;
}
.rwmb-clone-icon {
	cursor: move;
	background: url(../img/drag_icon.gif) no-repeat;
	height: 23px;
	width: 15px;
	vertical-align: top;
	display: inline-block;
	position: absolute;
	left: 0;
	top: 0;
}
.rwmb-sort-clone {
	padding-left: 15px;
}

/* jQuery validation */
p.rwmb-error {
	color: #dc3232;
	margin: 2px 0 5px;
}
input.rwmb-error.rwmb-error,
textarea.rwmb-error,
select.rwmb-error {
	border-color: #dc3232;
	background: #ffebe8;
}

/* Utilities
-------------------------------------------------------------- */
.rwmb-sortable-placeholder {
	background: #fcf8e3;
	border: 1px solid #faebcc;
	display: block;
}


/* Styles for 'side' meta boxes
-------------------------------------------------------------- */

#side-sortables .rwmb-label,
#side-sortables .rwmb-input {
	width: 100%;
}
#side-sortables .rwmb-label {
	margin-bottom: 3px;
}
#side-sortables .rwmb-input input,
#side-sortables .rwmb-input select {
	width: 100%;
	max-width: 100%;
}
#side-sortables .rwmb-input input[type="checkbox"],
#side-sortables .rwmb-input input[type="radio"] {
	width: auto;
}

/* Mobile style */
@media (max-width: 1023px) {
	.rwmb-label,
	.rwmb-label ~ .rwmb-input {
		width: 100%;
	}
	.rwmb-label {
		margin-bottom: 3px;
	}
	.rwmb-input input,
	.rwmb-input select {
		width: 100%;
		max-width: 100%;
	}
	.rwmb-input input[type="checkbox"],
	.rwmb-input input[type="radio"] {
		width: auto;
	}
}

/* Seamless style
--------------------------------------------------------------*/
.rwmb-seamless {
	background: none;
	border: none;
	box-shadow: none;
}
.rwmb-seamless .inside {
	padding-left: 0;
	padding-right: 0;
}
.postbox.rwmb-seamless .hndle,
.postbox.rwmb-seamless .handlediv {
	display: none;
}
.rwmb-seamless .rwmb-clone {
	background: none;
}

/* CSS fixes
--------------------------------------------------------------*/
/* Fix color picker field is hidden by the post editor at after_title position. https://metabox.io/support/topic/bug-color-picker-field-is-showed-below-the-title-field/ */
.postarea {
	position: relative;
	z-index: 0;
}