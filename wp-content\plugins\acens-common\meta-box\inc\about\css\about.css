.about-wrap {
	padding-right: 20px;
}
.about-wrap.about-wrap .wp-badge {
	background-image: url(../images/meta-box.svg);
	background-color: #fff;
	color: #222;
	text-transform: uppercase;
	font-weight: bold;
	text-decoration: none;
}

.wp-badge:hover {
	text-decoration: none;
}

.about-buttons .dashicons {
	position: relative;
	top: 5px;
	width: 16px;
	height: 16px;
	font-size: 16px;
}

#poststuff .nav-tab-wrapper {
	padding: 0;
	margin-top: 60px;
}

.nav-tab-active:focus {
	box-shadow: none;
}

.gt-tab-pane {
	display: none;
}

.gt-is-active {
	display: block;
}

.two {
	margin-top: 15px;
	display: flex;
}
.two .col + .col {
	margin-left: 40px;
}
.two h3:not(:first-child) {
	margin-top: 3em;
}
.two img {
	display: block;
	box-shadow: 0 0 20px rgba(0, 0, 0, .1);
	border-radius: 4px;
}
.screenshot {
	display: block;
	margin: 3em auto;
}
.col {
	flex: 1;
}
.col ul {
	font-size: 14px;
	margin: 2em 0;
}
.col li a {
	text-decoration: none;
}

/* Extensions tab */
.extensions {
	margin-top: 40px;
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
}
.extension {
	width: 100%;
	margin-bottom: 20px;
}
.extension-inner {
	display: flex;
	background: #fff;
	padding: 25px 20px;
}
.extension-inner svg {
	width: 36px;
	height: 36px;
	margin: 8px 15px 0 0;
	fill: #b4b9be;
}
.extension-info {
	flex: 1;
}
.extension-info.extension-info h3 {
	margin: 0 0 5px;
	font-size: 1.2em;
}
.extension-info p {
	margin: 0;
}
.extension-action {
	border-top: 1px solid #ddd;
	text-align: center;
	padding: 20px;
	background: #f7f7f7;
}

/* Extensions tab: 2 columns */
@media (min-width: 768px) {
	.extension {
		width: 49%;
	}
}
/* For large screen: 3 columns */
@media (min-width: 1600px) {
	.extension {
		width: 32%;
	}
}

/* Right column */
#post-body.columns-2 #postbox-container-1 {
	position: fixed;
	right: 320px;
	top: 80px;
}

@media (max-width: 1279px) {
	#postbox-container-1 {
		display: none;
	}
	#poststuff #post-body.columns-2 {
		margin-right: 0;
	}
}

/* Upgrade */
.upgrade {
	border: 3px dashed #82878c;
	background: #fff;
	padding: 15px;
}
.upgrade .dashicons {
	color: #ef4836;
	width: 26px;
	height: 26px;
	font-size: 30px;
	position: relative;
	top: -4px;
	left: -5px;
}
.upgrade h3 {
	margin: 0;
}
.upgrade li {
	padding-left: 20px;
	position: relative;
}
.upgrade svg {
	width: 1em;
	height: 1em;
	fill: #0073aa;
	position: absolute;
	top: 4px;
	left: 0;
}
