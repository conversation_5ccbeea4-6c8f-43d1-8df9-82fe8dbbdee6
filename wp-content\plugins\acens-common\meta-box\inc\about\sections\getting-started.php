<?php
/**
 * Getting started section.
 *
 * @package Meta Box
 */

?>
<div id="getting-started" class="gt-tab-pane gt-is-active">
	<div class="two">
		<div class="col">
			<h3><?php esc_html_e( 'Getting Started With Online Generator', 'meta-box' ); ?></h3>
			<p><?php esc_html_e( 'Online Generator is a free tool to help you create and set up custom fields using a simple, friendly user interface. With it, you can add fields, set options and generate needed code that\'s ready to copy and paste.', 'meta-box' ); ?></p>

			<a target="_blank" class="screenshot" href="https://metabox.io/online-generator/?utm_source=WordPress&utm_medium=link&utm_campaign=plugin"><img src="<?php echo esc_url( RWMB_URL . 'inc/about/images/online-generator.png' ); ?>" alt="<?php esc_attr_e( 'online generator', 'meta-box' ); ?>"></a>

			<p><a class="button" target="_blank" href="<?php echo esc_url( 'https://metabox.io/online-generator/?utm_source=WordPress&utm_medium=link&utm_campaign=plugin' ); ?>"><?php esc_html_e( 'Go to Online Generator', 'meta-box' ); ?></a></p>
		</div>

		<div class="col">
			<h3><?php esc_html_e( 'Understand The Basics', 'meta-box' ); ?></h3>
			<p><?php esc_html_e( 'Meta Box doesn\'t have any admin page for configuration or settings. Instead of that, it provides a very powerful API to speed up the process of creating meta boxes and custom fields. It might take you a little time at first, but then you\'ll love the way it work because it helps you do and customize almost everything.', 'meta-box' ); ?><p>
			<p><a class="button" target="_blank" href="<?php echo esc_url( 'https://docs.metabox.io/quick-start/?utm_source=WordPress&utm_medium=link&utm_campaign=plugin' ); ?>"><?php esc_html_e( 'Learn More', 'meta-box' ); ?></a></p>

			<h3><?php esc_html_e( 'Extensions', 'meta-box' ); ?></h3>
			<p><?php esc_html_e( 'Wanna see more features that transform your WordPress website into a powerful CMS? Check out some extensions below:', 'meta-box' ); ?><p>
			<ul>
				<li><a target="_blank" href="https://metabox.io/plugins/meta-box-builder/?utm_source=WordPress&utm_medium=link&utm_campaign=plugin"><?php esc_html_e( 'Meta Box Builder', 'meta-box' ); ?></a> - <?php esc_html_e( 'Build meta boxes and fields with UI.', 'meta-box' ); ?></li>
				<li><a target="_blank" href="https://metabox.io/plugins/meta-box-group/?utm_source=WordPress&utm_medium=link&utm_campaign=plugin"><?php esc_html_e( 'Meta Box Group', 'meta-box' ); ?></a> - <?php esc_html_e( 'Organize fields into repeatable groups.', 'meta-box' ); ?></li>
				<li><a target="_blank" href="https://metabox.io/plugins/meta-box-conditional-logic/?utm_source=WordPress&utm_medium=link&utm_campaign=plugin"><?php esc_html_e( 'Meta Box Conditional Logic', 'meta-box' ); ?></a> - <?php esc_html_e( 'Control the visibility of fields.', 'meta-box' ); ?></li>
				<li><a target="_blank" href="https://metabox.io/plugins/mb-settings-page/?utm_source=WordPress&utm_medium=link&utm_campaign=plugin"><?php esc_html_e( 'MB Settings Page', 'meta-box' ); ?></a> - <?php esc_html_e( 'Create settings pages/Customizer options.', 'meta-box' ); ?></li>
			</ul>
			<p><a class="button" target="_blank" href="https://metabox.io/plugins/?utm_source=WordPress&utm_medium=link&utm_campaign=plugin"><?php esc_html_e( 'More Extensions', 'meta-box' ); ?></a></p>
		</div>
	</div>
</div>
