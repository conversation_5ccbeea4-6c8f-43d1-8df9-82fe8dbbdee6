<?php

namespace BdevsElementor\Widget;

use <PERSON><PERSON>or\Controls_Manager;
use <PERSON>ementor\Group_Control_Typography;
use <PERSON><PERSON>or\Scheme_Typography;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;

/**
 * Bdevs Elementor Widget.
 *
 * Elementor widget that inserts blog post navigation into the page.
 *
 * @since 1.0.0
 */
class BdevsPostNavigation extends \Elementor\Widget_Base
{

	/**
	 * Get widget name.
	 *
	 * Retrieve Bdevs Elementor widget name.
	 *
	 * @since 1.0.0
	 * @access public
	 *
	 * @return string Widget name.
	 */
	public function get_name()
	{
		return 'bdevs-post-navigation';
	}

	/**
	 * Get widget title.
	 *
	 * Retrieve Bdevs Elementor widget title.
	 *
	 * @since 1.0.0
	 * @access public
	 *
	 * @return string Widget title.
	 */
	public function get_title()
	{
		return __('Post Navigation (Custom)', 'bdevs-elementor');
	}

	/**
	 * Get widget icon.
	 *
	 * Retrieve Bdevs Post Navigation widget icon.
	 *
	 * @since 1.0.0
	 * @access public
	 *
	 * @return string Widget icon.
	 */
	public function get_icon()
	{
		return 'eicon-post-navigation';
	}

	/**
	 * Get widget categories.
	 *
	 * Retrieve the list of categories the Bdevs Post Navigation widget belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 *
	 * @return array Widget categories.
	 */
	public function get_categories()
	{
		return ['blog-elementor'];
	}

	public function get_keywords()
	{
		return ['post', 'navigation', 'previous', 'next', 'blog'];
	}

	public function get_script_depends()
	{
		return ['bdevs-elementor'];
	}

	protected function _register_controls()
	{
		$this->start_controls_section(
			'section_content_post_navigation',
			[
				'label' => esc_html__('Post Navigation', 'bdevs-elementor'),
			]
		);

		$this->add_control(
			'fallback_image',
			[
				'label'       => esc_html__('Fallback Image', 'bdevs-elementor'),
				'type'        => Controls_Manager::MEDIA,
				'dynamic'     => ['active' => true],
				'label_block' => true,
				'description' => esc_html__('Image to show when post has no featured image', 'bdevs-elementor'),
				'default'     => [
					'url' => '/wp-content/uploads/2025/04/trds-archive-hero.webp',
				],
			]
		);

		$this->add_control(
			'show_thumbnails',
			[
				'label'        => esc_html__('Show Thumbnails', 'bdevs-elementor'),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => esc_html__('Show', 'bdevs-elementor'),
				'label_off'    => esc_html__('Hide', 'bdevs-elementor'),
				'return_value' => 'yes',
				'default'      => 'yes',
			]
		);

		$this->add_control(
			'previous_text',
			[
				'label'       => __('Previous Text', 'bdevs-elementor'),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __('Enter previous text', 'bdevs-elementor'),
				'default'     => __('Previous', 'bdevs-elementor'),
				'label_block' => true,
			]
		);

		$this->add_control(
			'next_text',
			[
				'label'       => __('Next Text', 'bdevs-elementor'),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __('Enter next text', 'bdevs-elementor'),
				'default'     => __('Next', 'bdevs-elementor'),
				'label_block' => true,
			]
		);

		// HTML Tag Controls
		$this->add_control(
			'html_tags_heading',
			[
				'label'     => esc_html__('HTML Tag Options', 'bdevs-elementor'),
				'type'      => Controls_Manager::HEADING,
				'separator' => 'before',
			]
		);

		$this->add_control(
			'nav_subtitle_tag',
			[
				'label'   => esc_html__('Navigation Text HTML Tag', 'bdevs-elementor'),
				'type'    => Controls_Manager::SELECT,
				'default' => 'span',
				'options' => [
					'span' => esc_html__('SPAN', 'bdevs-elementor'),
					'div'  => esc_html__('DIV', 'bdevs-elementor'),
					'p'    => esc_html__('P', 'bdevs-elementor'),
					'h1'   => esc_html__('H1', 'bdevs-elementor'),
					'h2'   => esc_html__('H2', 'bdevs-elementor'),
					'h3'   => esc_html__('H3', 'bdevs-elementor'),
					'h4'   => esc_html__('H4', 'bdevs-elementor'),
					'h5'   => esc_html__('H5', 'bdevs-elementor'),
					'h6'   => esc_html__('H6', 'bdevs-elementor'),
				],
				'description' => esc_html__('Choose the HTML tag for Previous/Next text. Default: SPAN', 'bdevs-elementor'),
			]
		);

		$this->add_control(
			'nav_title_tag',
			[
				'label'   => esc_html__('Post Title HTML Tag', 'bdevs-elementor'),
				'type'    => Controls_Manager::SELECT,
				'default' => 'span',
				'options' => [
					'span' => esc_html__('SPAN', 'bdevs-elementor'),
					'div'  => esc_html__('DIV', 'bdevs-elementor'),
					'p'    => esc_html__('P', 'bdevs-elementor'),
					'h1'   => esc_html__('H1', 'bdevs-elementor'),
					'h2'   => esc_html__('H2', 'bdevs-elementor'),
					'h3'   => esc_html__('H3', 'bdevs-elementor'),
					'h4'   => esc_html__('H4', 'bdevs-elementor'),
					'h5'   => esc_html__('H5', 'bdevs-elementor'),
					'h6'   => esc_html__('H6', 'bdevs-elementor'),
				],
				'description' => esc_html__('Choose the HTML tag for post titles in navigation. Default: SPAN', 'bdevs-elementor'),
			]
		);

		$this->end_controls_section();

		$this->start_controls_section(
			'section_content_layout',
			[
				'label' => esc_html__('Layout', 'bdevs-elementor'),
			]
		);

		$this->add_responsive_control(
			'align',
			[
				'label'   => esc_html__('Alignment', 'bdevs-elementor'),
				'type'    => Controls_Manager::CHOOSE,
				'options' => [
					'left' => [
						'title' => esc_html__('Left', 'bdevs-elementor'),
						'icon'  => 'fa fa-align-left',
					],
					'center' => [
						'title' => esc_html__('Center', 'bdevs-elementor'),
						'icon'  => 'fa fa-align-center',
					],
					'right' => [
						'title' => esc_html__('Right', 'bdevs-elementor'),
						'icon'  => 'fa fa-align-right',
					],
				],
				'prefix_class' => 'elementor%s-align-',
				'description'  => 'Use align to match position',
				'default'      => 'left',
			]
		);

		$this->end_controls_section();
	}

	public function render()
	{

		$settings  = $this->get_settings_for_display();
		extract($settings);

		// Get HTML tags with fallback to defaults
		$nav_subtitle_tag = !empty($settings['nav_subtitle_tag']) ? $settings['nav_subtitle_tag'] : 'span';
		$nav_title_tag = !empty($settings['nav_title_tag']) ? $settings['nav_title_tag'] : 'span';

		// Get previous and next posts
		$prev_post = get_previous_post();
		$next_post = get_next_post();

		// Only render if we have at least one navigation item
		if (!$prev_post && !$next_post) {
			if (\Elementor\Plugin::$instance->editor->is_edit_mode()) {
				echo '<div class="elementor-alert elementor-alert-warning">' . esc_html__('Post Navigation: No previous or next posts found. This widget will only display on single post pages with adjacent posts.', 'bdevs-elementor') . '</div>';
			}
			return;
		}

		$fallback_image_url = !empty($settings['fallback_image']['url']) ? $settings['fallback_image']['url'] : '/wp-content/uploads/2025/04/trds-archive-hero.webp';
?>

		<style>
			/* Widget-specific scoped CSS for Post Navigation - Conflict Prevention */
			/* Using unique class prefix to avoid conflicts with Elementor's .elementor-post-navigation */
			/* Ensure our widget doesn't inherit styles from Elementor's post navigation */
			.bdevs-post-navigation-widget {
				isolation: isolate;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation {
				margin: 40px 0 20px 0;
				padding: 30px 0;
				border-top: 1px solid #e8e8e8;
				border-bottom: 1px solid #e8e8e8;
				position: relative;
				z-index: 1;
				/* Reset any potential inherited styles */
				all: unset;
				display: block;
				margin: 40px 0 20px 0;
				padding: 30px 0;
				border-top: 1px solid #e8e8e8;
				border-bottom: 1px solid #e8e8e8;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-links {
				display: flex;
				justify-content: space-between;
				align-items: stretch;
				gap: 30px;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-previous,
			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-next {
				flex: 1;
				max-width: calc(50% - 15px);
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-previous a,
			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-next a {
				display: flex;
				align-items: center;
				padding: 20px;
				background: #fff;
				border: 1px solid #e8e8e8;
				border-radius: 8px;
				text-decoration: none;
				transition: all 0.3s ease;
				height: 100%;
				position: relative;
				gap: 15px;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-previous a:hover,
			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-next a:hover {
				background: #f8f9fa;
				border-color: var(--clr-theme-color);
				transform: translateY(-2px);
				box-shadow: 0 5px 15px rgba(171, 123, 42, 0.2);
			}

			/* Navigation Content Layout */
			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-content {
				display: flex;
				align-items: center;
				gap: 15px;
				flex: 1;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-next .bdevs-nav-content {
				flex-direction: row-reverse;
			}

			/* Navigation Images */
			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-image {
				width: 60px;
				height: 60px;
				border-radius: 8px;
				overflow: hidden;
				flex-shrink: 0;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-image img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: transform 0.3s ease;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation a:hover .bdevs-nav-image img {
				transform: scale(1.05);
			}

			/* Navigation Text */
			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-text {
				flex: 1;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-subtitle {
				display: block;
				font-size: clamp(0.875rem, calc(0.75rem + 0.25vw), 1rem);
				font-weight: 600;
				color: var(--clr-theme-color);
				margin-bottom: 5px;
				font-family: "Poppins", sans-serif;
				text-transform: uppercase;
				letter-spacing: 0.5px;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-title {
				display: block;
				font-size: clamp(0.875rem, calc(0.8rem + 0.15vw), 1rem);
				font-weight: 700;
				color: #2c3b4c;
				line-height: 1.3;
				font-family: "Poppins", sans-serif;
			}

			/* Navigation Arrows */
			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-arrow {
				width: 40px;
				height: 40px;
				background: var(--clr-theme-color);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-shrink: 0;
				transition: all 0.3s ease;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-arrow i {
				color: #fff;
				font-size: 16px;
				transition: transform 0.3s ease;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation a:hover .bdevs-nav-arrow {
				background: #2c3b4c;
				transform: scale(1.1);
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation a:hover .bdevs-nav-arrow i {
				color: #fff;
				transform: translateX(2px);
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-previous a:hover .bdevs-nav-arrow i {
				transform: translateX(-2px);
			}

			/* Text Alignment */
			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-previous .bdevs-nav-text {
				text-align: left;
			}

			.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-next .bdevs-nav-text {
				text-align: right;
			}

			/* Tablet Design */
			@media (max-width: 1024px) and (min-width: 769px) {
				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-links {
					gap: 25px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-image {
					width: 55px;
					height: 55px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-arrow {
					width: 38px;
					height: 38px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-arrow i {
					font-size: 15px;
				}
			}

			/* Mobile Design */
			@media (max-width: 768px) {
				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-links {
					flex-direction: column;
					gap: 20px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-previous,
				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-next {
					max-width: 100%;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-next .bdevs-nav-text {
					text-align: right;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation {
					margin: 30px 0 15px 0;
					padding: 20px 0;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-image {
					width: 50px;
					height: 50px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-arrow {
					width: 35px;
					height: 35px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-arrow i {
					font-size: 14px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-previous a,
				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-next a {
					padding: 15px;
					gap: 12px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-content {
					gap: 12px;
				}
			}

			/* Small Mobile Design */
			@media (max-width: 480px) {
				.bdevs-post-navigation-widget .bdevs-post-navigation {
					margin: 20px 0 10px 0;
					padding: 15px 0;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-links {
					gap: 15px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-image {
					width: 45px;
					height: 45px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-arrow {
					width: 32px;
					height: 32px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-arrow i {
					font-size: 12px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-previous a,
				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-next a {
					padding: 12px;
					gap: 10px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-content {
					gap: 10px;
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-subtitle {
					font-size: clamp(0.75rem, calc(0.7rem + 0.2vw), 0.875rem);
				}

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-title {
					font-size: clamp(0.75rem, calc(0.75rem + 0.1vw), 0.875rem);
				}
			}

			/* Ensure no conflicts with Elementor's built-in post navigation */
			.elementor-post-navigation+.bdevs-post-navigation-widget,
			.bdevs-post-navigation-widget+.elementor-post-navigation {
				margin-top: 40px;
			}

			/* Force isolation from other post navigation widgets */
			.bdevs-post-navigation-widget * {
				box-sizing: border-box;
			}

			/* Ensure proper touch targets on mobile */
			@media (max-width: 768px) {

				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-previous a,
				.bdevs-post-navigation-widget .bdevs-post-navigation .bdevs-nav-next a {
					min-height: 44px;
					/* iOS touch target minimum */
				}
			}
		</style>

		<div class="bdevs-post-navigation-widget">
			<div class="row">
				<div class="col-md-12">
					<nav class="bdevs-post-navigation" aria-label="<?php esc_attr_e('Post navigation', 'acens'); ?>">
						<div class="bdevs-nav-links">
							<?php if ($prev_post) :
								$prev_title = get_the_title($prev_post);
								$prev_url = get_permalink($prev_post);
								$prev_image = get_the_post_thumbnail_url($prev_post, 'thumbnail');
							?>
								<div class="bdevs-nav-previous">
									<a href="<?php echo esc_url($prev_url); ?>" rel="prev">
										<div class="bdevs-nav-content">
											<?php if ($settings['show_thumbnails'] === 'yes') : ?>
												<div class="bdevs-nav-image">
													<img src="<?php echo $prev_image ? esc_url($prev_image) : esc_url($fallback_image_url); ?>"
														alt="<?php echo esc_attr($prev_title); ?>" loading="lazy">
												</div>
											<?php endif; ?>
											<div class="bdevs-nav-text">
												<<?php echo esc_attr($nav_subtitle_tag); ?> class="section-header-description bdevs-nav-subtitle"><?php echo esc_html($settings['previous_text']); ?></<?php echo esc_attr($nav_subtitle_tag); ?>>
												<<?php echo esc_attr($nav_title_tag); ?> class="about-content-heading bdevs-nav-title"><?php echo esc_html($prev_title); ?></<?php echo esc_attr($nav_title_tag); ?>>
											</div>
											<div class="bdevs-nav-arrow">
												<i class="fas fa-chevron-left"></i>
											</div>
										</div>
									</a>
								</div>
							<?php endif; ?>

							<?php if ($next_post) :
								$next_title = get_the_title($next_post);
								$next_url = get_permalink($next_post);
								$next_image = get_the_post_thumbnail_url($next_post, 'thumbnail');
							?>
								<div class="bdevs-nav-next">
									<a href="<?php echo esc_url($next_url); ?>" rel="next">
										<div class="bdevs-nav-content">
											<?php if ($settings['show_thumbnails'] === 'yes') : ?>
												<div class="bdevs-nav-image">
													<img src="<?php echo $next_image ? esc_url($next_image) : esc_url($fallback_image_url); ?>"
														alt="<?php echo esc_attr($next_title); ?>" loading="lazy">
												</div>
											<?php endif; ?>
											<div class="bdevs-nav-text">
												<<?php echo esc_attr($nav_subtitle_tag); ?> class="section-header-description bdevs-nav-subtitle"><?php echo esc_html($settings['next_text']); ?></<?php echo esc_attr($nav_subtitle_tag); ?>>
												<<?php echo esc_attr($nav_title_tag); ?> class="about-content-heading bdevs-nav-title"><?php echo esc_html($next_title); ?></<?php echo esc_attr($nav_title_tag); ?>>
											</div>
											<div class="bdevs-nav-arrow">
												<i class="fas fa-chevron-right"></i>
											</div>
										</div>
									</a>
								</div>
							<?php endif; ?>
						</div>
					</nav>
				</div>
			</div>
		</div>
<?php
	}
}
