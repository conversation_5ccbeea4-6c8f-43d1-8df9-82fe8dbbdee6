<?php

namespace BdevsElementor\Widget;

use <PERSON>ementor\Controls_Manager;
use Elementor\Group_Control_Typography;
use Elementor\Scheme_Typography;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;

/**
 * Bdevs Elementor Widget.
 *
 * Elementor widget that combines parallax image functionality with modern card design and center mode carousel.
 *
 * @since 1.0.0
 */
class BdevsProjects3 extends \Elementor\Widget_Base
{

	/**
	 * Get widget name.
	 *
	 * Retrieve Bdevs Elementor widget name.
	 *
	 * @since 1.0.0
	 * @access public
	 *
	 * @return string Widget name.
	 */
	public function get_name()
	{
		return 'bdevs-projects3';
	}

	/**
	 * Get widget title.
	 *
	 * Retrieve Bdevs Elementor widget title.
	 *
	 * @since 1.0.0
	 * @access public
	 *
	 * @return string Widget title.
	 */
	public function get_title()
	{
		return __('Projects Home Parallax Image3', 'bdevs-elementor');
	}

	/**
	 * Get widget icon.
	 *
	 * Retrieve Bdevs Slider widget icon.
	 *
	 * @since 1.0.0
	 * @access public
	 *
	 * @return string Widget icon.
	 */
	public function get_icon()
	{
		return 'eicon-slideshow';
	}

	/**
	 * Get widget categories.
	 *
	 * Retrieve the list of categories the Bdevs Slider widget belongs to.
	 *
	 * @since 1.0.0
	 * @access public
	 *
	 * @return array Widget categories.
	 */
	public function get_categories()
	{
		return ['pages-elementor'];
	}

	public function get_keywords()
	{
		return ['Projects', 'carousel', 'cards', 'center mode'];
	}

	public function get_script_depends()
	{
		return ['bdevs-elementor'];
	}

	// BDT Position
	protected function element_pack_position()
	{
		$position_options = [
			''              => esc_html__('Default', 'bdevs-elementor'),
			'top-left'      => esc_html__('Top Left', 'bdevs-elementor'),
			'top-center'    => esc_html__('Top Center', 'bdevs-elementor'),
			'top-right'     => esc_html__('Top Right', 'bdevs-elementor'),
			'center'        => esc_html__('Center', 'bdevs-elementor'),
			'center-left'   => esc_html__('Center Left', 'bdevs-elementor'),
			'center-right'  => esc_html__('Center Right', 'bdevs-elementor'),
			'bottom-left'   => esc_html__('Bottom Left', 'bdevs-elementor'),
			'bottom-center' => esc_html__('Bottom Center', 'bdevs-elementor'),
			'bottom-right'  => esc_html__('Bottom Right', 'bdevs-elementor'),
		];

		return $position_options;
	}

	protected function _register_controls()
	{
		$this->start_controls_section(
			'section_content_Projects',
			[
				'label' => esc_html__('Projects', 'bdevs-elementor'),
			]
		);
		$this->add_control(
			'subheading',
			[
				'label'       => __('Subheading', 'bdevs-elementor'),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __('Enter your sub heading', 'bdevs-elementor'),
				'default'     => __('Portfolio & Project', 'bdevs-elementor'),
				'label_block' => true,
			]
		);

		$this->add_control(
			'heading',
			[
				'label'       => __('Heading', 'bdevs-elementor'),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __('Enter your heading', 'bdevs-elementor'),
				'default'     => __('Our Works', 'bdevs-elementor'),
				'label_block' => true,
			]
		);

		$this->add_control(
			'post_number',
			[
				'label'     => esc_html__('Post Count', 'bdevs-elementor'),
				'type'      => Controls_Manager::TEXT,
				'default'   => '6',
			]
		);
		$this->add_control(
			'orderpost',
			[
				'label'     => esc_html__('Post Order', 'bdevs-elementor'),
				'type'      => Controls_Manager::SELECT,
				'options'   => [
					'asc'  => esc_html__('ASC', 'bdevs-elementor'),
					'desc' => esc_html__('DESC', 'bdevs-elementor'),
				],
				'default'   => 'desc',
			]
		);

		$this->add_control(
			'orderby',
			[
				'label'     => esc_html__('Order By', 'bdevs-elementor'),
				'type'      => Controls_Manager::SELECT,
				'options'   => [
					'date'  => esc_html__('Date', 'bdevs-elementor'),
					'title' => esc_html__('Title', 'bdevs-elementor'),
					'rand' => esc_html__('Random', 'bdevs-elementor'),
				],
				'default'   => '',
			]
		);

		// Add button controls
		$this->add_control(
			'button_text',
			[
				'label' => __('Button Text', 'bdevs-elementor'),
				'type' => Controls_Manager::TEXT,
				'default' => __('View All Projects', 'bdevs-elementor'),
				'placeholder' => __('Enter button text', 'bdevs-elementor'),
			]
		);
		$this->add_control(
			'button_url',
			[
				'label' => __('Button URL', 'bdevs-elementor'),
				'type' => Controls_Manager::URL,
				'placeholder' => __('https://your-link.com', 'bdevs-elementor'),
				'default' => [
					'url' => '',
					'is_external' => false,
					'nofollow' => false,
				],
			]
		);

		/**
		 * Add Show/Hide Author Toggle
		 */
		$this->add_control(
			'show_author',
			[
				'label' => __('Show Author', 'bdevs-elementor'),
				'type' => \Elementor\Controls_Manager::SWITCHER,
				'label_on' => __('Show', 'bdevs-elementor'),
				'label_off' => __('Hide', 'bdevs-elementor'),
				'return_value' => 'yes',
				'default' => 'yes',
			]
		);
		/**
		 * Add Show/Hide Date Toggle
		 */
		$this->add_control(
			'show_date',
			[
				'label' => __('Show Date', 'bdevs-elementor'),
				'type' => \Elementor\Controls_Manager::SWITCHER,
				'label_on' => __('Show', 'bdevs-elementor'),
				'label_off' => __('Hide', 'bdevs-elementor'),
				'return_value' => 'yes',
				'default' => 'yes',
			]
		);
		/**
		 * Add Show/Hide Category Toggle
		 */
		$this->add_control(
			'show_category',
			[
				'label' => __('Show Category', 'bdevs-elementor'),
				'type' => \Elementor\Controls_Manager::SWITCHER,
				'label_on' => __('Show', 'bdevs-elementor'),
				'label_off' => __('Hide', 'bdevs-elementor'),
				'return_value' => 'yes',
				'default' => 'yes',
			]
		);
		/**
		 * Add Show/Hide Excerpt Toggle
		 */
		$this->add_control(
			'show_excerpt',
			[
				'label' => __('Show Excerpt', 'bdevs-elementor'),
				'type' => \Elementor\Controls_Manager::SWITCHER,
				'label_on' => __('Show', 'bdevs-elementor'),
				'label_off' => __('Hide', 'bdevs-elementor'),
				'return_value' => 'yes',
				'default' => 'yes',
			]
		);

		$this->end_controls_section();

		// Add Post Filtering Section
		$this->start_controls_section(
			'section_post_filtering',
			[
				'label' => esc_html__('Post Filtering', 'bdevs-elementor'),
			]
		);

		// Category Filter
		$project_categories = get_terms(array(
			'taxonomy' => 'project_category',
			'hide_empty' => false,
		));
		$category_options = array('' => esc_html__('All Categories', 'bdevs-elementor'));
		if (!is_wp_error($project_categories) && !empty($project_categories)) {
			foreach ($project_categories as $category) {
				$category_options[$category->term_id] = $category->name;
			}
		}

		$this->add_control(
			'category_filter',
			[
				'label' => esc_html__('Filter by Category', 'bdevs-elementor'),
				'type' => Controls_Manager::SELECT,
				'options' => $category_options,
				'default' => '',
				'description' => esc_html__('Select a specific category to display only projects from that category.', 'bdevs-elementor'),
			]
		);

		// Featured Posts Only Toggle
		$this->add_control(
			'featured_only',
			[
				'label' => __('Featured Posts Only', 'bdevs-elementor'),
				'type' => \Elementor\Controls_Manager::SWITCHER,
				'label_on' => __('Yes', 'bdevs-elementor'),
				'label_off' => __('No', 'bdevs-elementor'),
				'return_value' => 'yes',
				'default' => 'no',
				'description' => __('Show only posts marked as featured (sticky posts).', 'bdevs-elementor'),
			]
		);

		// Exclude Post IDs
		$this->add_control(
			'exclude_posts',
			[
				'label' => __('Exclude Post IDs', 'bdevs-elementor'),
				'type' => Controls_Manager::TEXT,
				'placeholder' => __('1,2,3', 'bdevs-elementor'),
				'description' => __('Enter post IDs to exclude, separated by commas.', 'bdevs-elementor'),
			]
		);

		// Date Range Filter
		$this->add_control(
			'date_filter',
			[
				'label' => esc_html__('Date Filter', 'bdevs-elementor'),
				'type' => Controls_Manager::SELECT,
				'options' => [
					'' => esc_html__('All Time', 'bdevs-elementor'),
					'7' => esc_html__('Last 7 Days', 'bdevs-elementor'),
					'30' => esc_html__('Last 30 Days', 'bdevs-elementor'),
					'90' => esc_html__('Last 3 Months', 'bdevs-elementor'),
					'180' => esc_html__('Last 6 Months', 'bdevs-elementor'),
					'365' => esc_html__('Last Year', 'bdevs-elementor'),
				],
				'default' => '',
				'description' => esc_html__('Filter posts by publication date.', 'bdevs-elementor'),
			]
		);

		// Meta Key Filter (for custom fields)
		$this->add_control(
			'meta_key_filter',
			[
				'label' => __('Meta Key Filter', 'bdevs-elementor'),
				'type' => Controls_Manager::TEXT,
				'placeholder' => __('_featured_project', 'bdevs-elementor'),
				'description' => __('Filter by custom meta key (optional).', 'bdevs-elementor'),
			]
		);

		$this->add_control(
			'meta_value_filter',
			[
				'label' => __('Meta Value Filter', 'bdevs-elementor'),
				'type' => Controls_Manager::TEXT,
				'placeholder' => __('yes', 'bdevs-elementor'),
				'description' => __('Meta value to match (required if meta key is set).', 'bdevs-elementor'),
				'condition' => [
					'meta_key_filter!' => '',
				],
			]
		);

		$this->end_controls_section();

		// HTML Tag Controls for Section Header
		$this->start_controls_section(
			'section_html_tags',
			[
				'label' => esc_html__('HTML Tag Options', 'bdevs-elementor'),
			]
		);

		$this->add_control(
			'section_subheading_tag',
			[
				'label'   => esc_html__('Subheading HTML Tag', 'bdevs-elementor'),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h6',
				'options' => [
					'h1'   => esc_html__('H1', 'bdevs-elementor'),
					'h2'   => esc_html__('H2', 'bdevs-elementor'),
					'h3'   => esc_html__('H3', 'bdevs-elementor'),
					'h4'   => esc_html__('H4', 'bdevs-elementor'),
					'h5'   => esc_html__('H5', 'bdevs-elementor'),
					'h6'   => esc_html__('H6', 'bdevs-elementor'),
					'div'  => esc_html__('DIV', 'bdevs-elementor'),
					'span' => esc_html__('SPAN', 'bdevs-elementor'),
					'p'    => esc_html__('P', 'bdevs-elementor'),
				],
				'description' => esc_html__('Choose the HTML tag for semantic structure and SEO. Default: H6', 'bdevs-elementor'),
			]
		);

		$this->add_control(
			'section_heading_tag',
			[
				'label'   => esc_html__('Heading HTML Tag', 'bdevs-elementor'),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h1',
				'options' => [
					'h1'   => esc_html__('H1', 'bdevs-elementor'),
					'h2'   => esc_html__('H2', 'bdevs-elementor'),
					'h3'   => esc_html__('H3', 'bdevs-elementor'),
					'h4'   => esc_html__('H4', 'bdevs-elementor'),
					'h5'   => esc_html__('H5', 'bdevs-elementor'),
					'h6'   => esc_html__('H6', 'bdevs-elementor'),
					'div'  => esc_html__('DIV', 'bdevs-elementor'),
					'span' => esc_html__('SPAN', 'bdevs-elementor'),
					'p'    => esc_html__('P', 'bdevs-elementor'),
				],
				'description' => esc_html__('Choose the HTML tag for semantic structure and SEO. Default: H1', 'bdevs-elementor'),
			]
		);

		$this->add_control(
			'project_title_tag',
			[
				'label'   => esc_html__('Project Title HTML Tag', 'bdevs-elementor'),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h2',
				'options' => [
					'h1'   => esc_html__('H1', 'bdevs-elementor'),
					'h2'   => esc_html__('H2', 'bdevs-elementor'),
					'h3'   => esc_html__('H3', 'bdevs-elementor'),
					'h4'   => esc_html__('H4', 'bdevs-elementor'),
					'h5'   => esc_html__('H5', 'bdevs-elementor'),
					'h6'   => esc_html__('H6', 'bdevs-elementor'),
					'div'  => esc_html__('DIV', 'bdevs-elementor'),
					'span' => esc_html__('SPAN', 'bdevs-elementor'),
					'p'    => esc_html__('P', 'bdevs-elementor'),
				],
				'description' => esc_html__('Choose the HTML tag for project titles. Default: H2', 'bdevs-elementor'),
			]
		);

		$this->end_controls_section();

		$this->start_controls_section(
			'section_content_layout',
			[
				'label' => esc_html__('Layout', 'bdevs-elementor'),
			]
		);

		$this->add_responsive_control(
			'align',
			[
				'label'   => esc_html__('Alignment', 'bdevs-elementor'),
				'type'    => Controls_Manager::CHOOSE,
				'options' => [
					'left' => [
						'title' => esc_html__('Left', 'bdevs-elementor'),
						'icon'  => 'fa fa-align-left',
					],
					'center' => [
						'title' => esc_html__('Center', 'bdevs-elementor'),
						'icon'  => 'fa fa-align-center',
					],
					'right' => [
						'title' => esc_html__('Right', 'bdevs-elementor'),
						'icon'  => 'fa fa-align-right',
					],
					'justify' => [
						'title' => esc_html__('Justified', 'bdevs-elementor'),
						'icon'  => 'fa fa-align-justify',
					],
				],
				'prefix_class' => 'elementor%s-align-',
				'description'  => 'Use align to match position',
				'default'      => 'left',
			]
		);

		$this->end_controls_section();

		// Add Style tab for meta fields
		$this->start_controls_section(
			'section_style_meta',
			[
				'label' => __('Meta', 'bdevs-elementor'),
				'tab' => \Elementor\Controls_Manager::TAB_STYLE,
			]
		);

		$this->add_control(
			'meta_color',
			[
				'label' => __('Meta Text Color', 'bdevs-elementor'),
				'type' => \Elementor\Controls_Manager::COLOR,
				'selectors' => [
					'{{WRAPPER}} .projects2-card-meta, {{WRAPPER}} .projects2-card-meta span' => 'color: {{VALUE}} !important;',
				],
			]
		);

		$this->add_control(
			'meta_link_color',
			[
				'label' => __('Category Link Color', 'bdevs-elementor'),
				'type' => \Elementor\Controls_Manager::COLOR,
				'selectors' => [
					'{{WRAPPER}} .projects2-card-category-link, {{WRAPPER}} .projects2-card-category-link:visited' => 'color: {{VALUE}} !important;',
				],
				'separator' => 'before',
			]
		);

		$this->add_control(
			'meta_link_hover_color',
			[
				'label' => __('Category Link Hover Color', 'bdevs-elementor'),
				'type' => \Elementor\Controls_Manager::COLOR,
				'selectors' => [
					'{{WRAPPER}} .projects2-card-category-link:hover, {{WRAPPER}} .projects2-card-category-link:focus' => 'color: {{VALUE}} !important;',
				],
			]
		);

		$this->add_group_control(
			\Elementor\Group_Control_Typography::get_type(),
			[
				'name' => 'meta_typography',
				'selector' => '{{WRAPPER}} .projects2-card-meta, {{WRAPPER}} .projects2-card-meta span',
			]
		);

		$this->add_group_control(
			\Elementor\Group_Control_Typography::get_type(),
			[
				'name' => 'category_typography',
				'label' => __('Category Typography', 'bdevs-elementor'),
				'selector' => '{{WRAPPER}} .projects2-card-category-link',
				'separator' => 'before',
			]
		);

		$this->add_responsive_control(
			'meta_spacing',
			[
				'label' => __('Meta Spacing', 'bdevs-elementor'),
				'type' => \Elementor\Controls_Manager::SLIDER,
				'range' => [
					'px' => ['min' => 0, 'max' => 50],
				],
				'selectors' => [
					'{{WRAPPER}} .projects2-card-meta span' => 'margin-right: {{SIZE}}{{UNIT}} !important;',
				],
			]
		);

		$this->end_controls_section();
	}

	public function render()
	{
		$settings  = $this->get_settings_for_display();

		// Get HTML tags for section header with fallback to defaults
		$section_subheading_tag = !empty($settings['section_subheading_tag']) ? $settings['section_subheading_tag'] : 'h6';
		$section_heading_tag = !empty($settings['section_heading_tag']) ? $settings['section_heading_tag'] : 'h1';
		$project_title_tag = !empty($settings['project_title_tag']) ? $settings['project_title_tag'] : 'h2';

		// Build query arguments with filtering
		$query_args = array(
			'post_type' => 'project',
			'posts_per_page' => $settings['post_number'],
			'orderby' => $settings['orderby'],
			'order' => $settings['orderpost'],
			'post_status' => 'publish',
		);

		// Category Filter
		if (!empty($settings['category_filter'])) {
			$query_args['tax_query'] = array(
				array(
					'taxonomy' => 'project_category',
					'field'    => 'term_id',
					'terms'    => $settings['category_filter'],
				),
			);
		}

		// Featured Posts Only
		if ('yes' === $settings['featured_only']) {
			$query_args['meta_query'] = array(
				array(
					'key'     => '_featured_project',
					'value'   => 'yes',
					'compare' => '=',
				),
			);
		}

		// Exclude Posts
		if (!empty($settings['exclude_posts'])) {
			$exclude_ids = array_map('trim', explode(',', $settings['exclude_posts']));
			$exclude_ids = array_filter($exclude_ids, 'is_numeric');
			if (!empty($exclude_ids)) {
				$query_args['post__not_in'] = $exclude_ids;
			}
		}

		// Date Filter
		if (!empty($settings['date_filter'])) {
			$days = intval($settings['date_filter']);
			$query_args['date_query'] = array(
				array(
					'after' => $days . ' days ago',
				),
			);
		}

		// Meta Key/Value Filter
		if (!empty($settings['meta_key_filter']) && !empty($settings['meta_value_filter'])) {
			if (!isset($query_args['meta_query'])) {
				$query_args['meta_query'] = array();
			}
			$query_args['meta_query'][] = array(
				'key'     => $settings['meta_key_filter'],
				'value'   => $settings['meta_value_filter'],
				'compare' => '=',
			);
		}

		// Handle multiple meta queries
		if (isset($query_args['meta_query']) && count($query_args['meta_query']) > 1) {
			$query_args['meta_query']['relation'] = 'AND';
		}

		$wp_query = new \WP_Query($query_args);
		extract($settings);
?>
		<section id="works" data-scroll-index="2" class="works works-image3 section-padding bg-drk">
			<div class="container">
				<div class="row mb-30">
					<div class="col-md-12 text-center">
						<?php if ('' !== $settings['subheading']) : ?>
							<<?php echo esc_attr($section_subheading_tag); ?> class="wow" data-splitting><?php echo wp_kses_post($settings['subheading']); ?></<?php echo esc_attr($section_subheading_tag); ?>>
						<?php endif; ?>
						<?php if ('' !== $settings['heading']) : ?>
							<<?php echo esc_attr($section_heading_tag); ?> class="wow" data-splitting><?php echo wp_kses_post($settings['heading']); ?></<?php echo esc_attr($section_heading_tag); ?>>
						<?php endif; ?>
						<div class="line-hr-section center"></div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<div class="owl-carousel owl-theme">
							<?php
							$i = 0;
							while ($wp_query->have_posts()): $wp_query->the_post();
								$img_featured = get_post_meta(get_the_ID(), '_cmb_img_featured', true);
								$i++;
							?>
								<div class="item">
									<div class="projects2-card">
										<?php if (wp_get_attachment_url($img_featured) != '') { ?>
											<img class="projects2-card-img" src="<?php echo wp_get_attachment_url($img_featured); ?>" alt="<?php echo esc_attr(get_the_title() . ' - Project Image'); ?>">
										<?php } elseif (has_post_thumbnail()) { ?>
											<img class="projects2-card-img" src="<?php echo get_the_post_thumbnail_url(); ?>" alt="<?php echo esc_attr(get_the_title() . ' - Project Image'); ?>">
										<?php } else { ?>
											<img class="projects2-card-img" src="/wp-content/uploads/2025/04/trds-archive-hero.webp" alt="<?php echo esc_attr(get_the_title() . ' - Default Project Image'); ?>">
										<?php } ?>
										<div class="projects2-card-body">
											<?php if ('yes' === $settings['show_category']) : ?>
												<?php
												$term_list = get_the_terms(get_the_ID(), 'project_category');
												if (!is_wp_error($term_list) && !empty($term_list)) {
													$term_count = count($term_list);
													$container_class = ($term_count > 1) ? 'multiple-tags' : 'single-tag';
													echo '<div class="projects2-card-categories ' . $container_class . '">';
													foreach ($term_list as $j => $term) {
														$cat_link = get_term_link($term);
														if (!is_wp_error($cat_link)) {
															echo '<a href="' . esc_url($cat_link) . '" class="projects2-card-category-link">' . esc_html($term->name) . '</a>';
														}
													}
													echo '</div>';
												}
												?>
											<?php endif; ?>
											<div class="projects2-card-title">
												<<?php echo esc_attr($project_title_tag); ?>><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></<?php echo esc_attr($project_title_tag); ?>>
											</div>
											<?php if ('yes' === $settings['show_excerpt']) : ?>
												<div class="projects2-card-excerpt">
													<?php echo wp_trim_words(get_the_excerpt(), 18, '...'); ?>
												</div>
											<?php endif; ?>
											<div class="projects2-card-meta">
												<?php if ('yes' === $settings['show_author']) : ?>
													<?php echo get_avatar(get_the_author_meta('ID'), 36, '', '', ['class' => 'projects2-card-avatar']); ?>
													<span class="projects2-card-author"><?php the_author(); ?></span>
												<?php endif; ?>
												<?php if ('yes' === $settings['show_date']) : ?>
													<span class="projects2-card-date"><?php echo get_the_date(); ?></span>
												<?php endif; ?>
											</div>
										</div>
									</div>
								</div>
							<?php endwhile;
							wp_reset_postdata(); // Reset post data after custom query
							?>
						</div>
					</div>
				</div>
				<?php
				// Render the button if text and url are set (now below the carousel)
				if (! empty($settings['button_text']) && ! empty($settings['button_url']['url'])) {
					$is_external = $settings['button_url']['is_external'] ? ' target="_blank"' : '';
					$nofollow = $settings['button_url']['nofollow'] ? ' rel="nofollow"' : '';
					echo '<div class="row"><div class="col-md-12 text-center mt-30">';
					echo '<a class="project-parallax-btn btn-3 mt-15" href="' . esc_url($settings['button_url']['url']) . '"' . $is_external . $nofollow . '>';
					echo esc_html($settings['button_text']);
					echo '</a>';
					echo '</div></div>';
				}
				?>
			</div>
		</section>

		<!-- Custom styles for Projects Home Parallax Image3 widget with CENTER MODE -->
		<style>
			/* Target ONLY the Image3 widget using unique class */
			/* Center Mode Carousel with larger center item */
			.works-image3 .owl-carousel .owl-stage-outer {
				overflow: hidden !important;
				/* Essential for proper carousel functionality */
			}

			.works-image3 .owl-carousel .owl-stage {
				display: flex !important;
				align-items: stretch !important;
			}

			.works-image3 .owl-carousel .owl-item {
				display: flex !important;
				flex-direction: column !important;
				height: auto !important;
				transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
				opacity: 0.75 !important;
				/* Side items have reduced opacity */
				transform: scale(0.9) !important;
				/* Side items are smaller */
				z-index: 1 !important;
			}

			.works-image3 .owl-carousel .owl-item.active {
				display: flex !important;
			}

			/* Center item styling - larger and full opacity */
			.works-image3 .owl-carousel .owl-item.center {
				opacity: 1 !important;
				/* Full opacity for center item */
				transform: scale(1.15) !important;
				/* Center item is larger */
				z-index: 10 !important;
				/* Higher z-index for center item */
			}

			/* Ensure center item maintains border radius when scaled */
			.works-image3 .owl-carousel .owl-item.center .projects2-card {
				border-radius: 16px !important;
				/* Maintain rounded corners */
				overflow: visible !important;
				/* Allow border-radius to be visible when scaled */
				box-shadow: 0 12px 40px 0 rgba(30, 34, 90, 0.18) !important;
				/* Enhanced shadow for center item */
			}

			/* Fallback for when .center class is not applied - target middle item */
			.works-image3 .owl-carousel .owl-item:nth-child(2) {
				opacity: 1 !important;
				transform: scale(1.15) !important;
				z-index: 10 !important;
			}

			/* Ensure fallback center item maintains border radius when scaled */
			.works-image3 .owl-carousel .owl-item:nth-child(2) .projects2-card {
				border-radius: 16px !important;
				/* Maintain rounded corners */
				overflow: visible !important;
				/* Allow border-radius to be visible when scaled */
				box-shadow: 0 12px 40px 0 rgba(30, 34, 90, 0.18) !important;
				/* Enhanced shadow for center item */
			}

			.works-image3 .owl-carousel .item {
				display: flex !important;
				flex-direction: column !important;
				height: 100% !important;
				width: 100% !important;
			}

			.works-image3 .owl-carousel .item .projects2-card {
				display: flex !important;
				flex-direction: column !important;
				height: 100% !important;
				width: 100% !important;
				flex: 1 !important;
				min-height: 450px !important;
				background: var(--clr-bg, #fff) !important;
				border: none !important;
				border-radius: 16px !important;
				box-shadow: 0 8px 32px 0 rgba(30, 34, 90, 0.13) !important;
				overflow: hidden !important;
				/* Keep content within card bounds */
				transition: box-shadow 0.3s cubic-bezier(.4, 2, .6, 1), transform 0.3s cubic-bezier(.4, 2, .6, 1), border-radius 0.3s ease !important;
				transform-origin: center center !important;
				backface-visibility: hidden !important;
				position: relative !important;
				/* Ensure proper stacking context */
			}

			.works-image3 .owl-carousel .item .projects2-card-body {
				display: flex !important;
				flex-direction: column !important;
				flex: 1 !important;
				padding: clamp(16px, 3vw, 32px) clamp(12px, 2vw, 28px) clamp(12px, 2vw, 22px) clamp(12px, 2vw, 28px) !important;
				background: var(--clr-bg, #fff) !important;
				border-bottom-left-radius: 16px !important;
				border-bottom-right-radius: 16px !important;
			}

			/* Content area flexibility */
			.works-image3 .owl-carousel .item .projects2-card-categories {
				flex: 0 0 auto !important;
				margin-bottom: clamp(10px, 1.2vw, 18px) !important;
			}

			.works-image3 .owl-carousel .item .projects2-card-title {
				flex: 0 0 auto !important;
				margin-bottom: clamp(8px, 1.5vw, 16px) !important;
			}

			.works-image3 .owl-carousel .item .projects2-card-excerpt {
				flex: 1 1 auto !important;
				margin-bottom: clamp(14px, 2vw, 26px) !important;
			}

			.works-image3 .owl-carousel .item .projects2-card-meta {
				flex: 0 0 auto !important;
				margin-top: auto !important;
				padding-top: clamp(6px, 1vw, 14px) !important;
				border-top: 1px solid #f0f0f0 !important;
			}

			/* When excerpt is hidden, adjust title spacing */
			.works-image3 .owl-carousel .item .projects2-card-title:last-of-type {
				margin-bottom: clamp(14px, 2vw, 26px) !important;
			}

			/* Hover effects - Enhanced for center mode */
			.works-image3 .owl-carousel .item .projects2-card:hover {
				box-shadow: 0 20px 50px 0 rgba(30, 34, 90, 0.25) !important;
				transform: translateY(-5px) !important;
				transform-origin: center center !important;
				border: solid 1px #ba7b24 !important;
				border-radius: 16px !important;
				overflow: hidden !important;
				will-change: transform, box-shadow !important;
			}

			/* Special hover effects for center items to maintain scaling + hover */
			.works-image3 .owl-carousel .owl-item.center .projects2-card:hover {
				transform: scale(1.15) translateY(-5px) !important;
				/* Maintain center scale + hover lift */
				border-radius: 16px !important;
				/* Ensure radius is preserved */
			}

			/* Fallback hover for nth-child center item */
			@media (min-width: 1000px) {
				.works-image3 .owl-carousel .owl-item.active:nth-child(2) .projects2-card:hover {
					transform: scale(1.15) translateY(-5px) !important;
					/* Maintain center scale + hover lift */
					border-radius: 16px !important;
					/* Ensure radius is preserved */
				}
			}

			.works-image3 .owl-carousel .item .projects2-card:hover .projects2-card-img {
				transform: scale(1.05) !important;
				border-top-left-radius: 16px !important;
				border-top-right-radius: 16px !important;
			}

			/* Smaller avatar size for carousel cards */
			.works-image3 .owl-carousel .projects2-card-avatar {
				width: 24px !important;
				height: 24px !important;
				margin-right: 8px !important;
				border-radius: 50% !important;
				object-fit: cover !important;
				border: 2px solid #f3f3f3 !important;
				box-shadow: 0 2px 8px 0 rgba(30, 34, 90, 0.08) !important;
			}

			/* Ensure consistent image heights */
			.works-image3 .owl-carousel .projects2-card-img {
				height: 200px !important;
				object-fit: cover !important;
				width: 100% !important;
				border-top-left-radius: 16px !important;
				border-top-right-radius: 16px !important;
				transition: transform 0.4s cubic-bezier(.4, 2, .6, 1) !important;
				flex: 0 0 auto !important;
			}

			/* Content distribution within cards */
			.works-image3 .owl-carousel .item .projects2-card-title h2 {
				margin-bottom: clamp(8px, 1.5vw, 16px) !important;
				line-height: 1.3 !important;
			}

			.works-image3 .owl-carousel .item .projects2-card-title h2 a {
				color: inherit !important;
				text-decoration: none !important;
			}

			.works-image3 .owl-carousel .item .projects2-card-excerpt {
				overflow: hidden !important;
				display: -webkit-box !important;
				-webkit-line-clamp: 3 !important;
				-webkit-box-orient: vertical !important;
				text-overflow: ellipsis !important;
			}

			/* Meta styling */
			.works-image3 .owl-carousel .item .projects2-card-meta {
				display: flex !important;
				align-items: center !important;
				gap: clamp(0.375rem, 1vw, 0.5rem) !important;
			}

			.works-image3 .owl-carousel .item .projects2-card-author,
			.works-image3 .owl-carousel .item .projects2-card-date {
				font-size: clamp(11px, 1vw, 15px) !important;
				color: #a0a0a0 !important;
			}

			.works-image3 .owl-carousel .item .projects2-card-author {
				margin-right: clamp(0.3125rem, 1vw, 0.75rem) !important;
			}

			/* Category styling - Match original Projects2 widget design */
			.works-image3 .owl-carousel .item .projects2-card-categories {
				text-align: left !important;
				margin-bottom: clamp(10px, 1.2vw, 18px) !important;
			}

			.works-image3 .owl-carousel .item .projects2-card-categories.multiple-tags {
				display: flex !important;
				justify-content: flex-start !important;
				flex-wrap: wrap !important;
				gap: 5px !important;
			}

			.works-image3 .owl-carousel .item .projects2-card-category-link {
				display: inline-block !important;
				width: auto !important;
				max-width: fit-content !important;
				flex: 0 0 auto !important;
				min-width: 0 !important;
				padding: clamp(8px, 1vw, 10px) clamp(10px, 1.5vw, 12px) !important;
				margin-right: clamp(4px, 1vw, 10px) !important;
				background: #b8862b !important;
				color: #fff !important;
				font-size: clamp(11px, 1.1vw, 14px) !important;
				font-weight: 500 !important;
				line-height: 0 !important;
				text-align: left !important;
				letter-spacing: 0.02em !important;
				white-space: nowrap !important;
				border: none !important;
				border-radius: 999px !important;
				box-shadow: 0 2px 8px 0 rgba(184, 134, 43, 0.10) !important;
				text-decoration: none !important;
				transition: background 0.2s, color 0.2s, transform 0.2s, box-shadow 0.2s !important;
			}

			.works-image3 .owl-carousel .item .projects2-card-category-link:hover,
			.works-image3 .owl-carousel .item .projects2-card-category-link:focus {
				background: #2c3b4c !important;
				color: #fff !important;
				text-decoration: none !important;
				transform: translateY(-1px) !important;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
			}

			/* Responsive adjustments for center mode */
			@media (max-width: 991px) {

				/* Disable center mode scaling on tablets (2 items) - looks weird with only 2 items */
				.works-image3 .owl-carousel .owl-item {
					transform: scale(1) !important;
					/* Equal scaling for 2-item layout */
					opacity: 1 !important;
					/* Equal opacity for 2-item layout */
				}

				.works-image3 .owl-carousel .owl-item.center,
				.works-image3 .owl-carousel .owl-item.active:nth-child(2) {
					transform: scale(1) !important;
					/* No center scaling on tablets */
					opacity: 1 !important;
					/* Equal opacity */
				}

				.works-image3 .owl-carousel .item .projects2-card {
					min-height: 400px !important;
				}

				.works-image3 .owl-carousel .projects2-card-img {
					height: 180px !important;
				}

				.works-image3 .owl-carousel .item .projects2-card-excerpt {
					-webkit-line-clamp: 2 !important;
				}
			}

			@media (max-width: 575px) {
				.works-image3 .owl-carousel .owl-stage-outer {
					padding: 0 !important;
					/* Remove extra padding */
				}

				.works-image3 .owl-carousel .owl-item {
					transform: scale(1) !important;
					/* No scaling on mobile - single item view */
					opacity: 1 !important;
					margin: 0 !important;
					/* Remove center item margins */
				}

				.works-image3 .owl-carousel .owl-item.center,
				.works-image3 .owl-carousel .owl-item.active:nth-child(2) {
					transform: scale(1) !important;
					margin: 0 !important;
					/* Remove center item margins */
				}

				.works-image3 .owl-carousel .item .projects2-card {
					min-height: 350px !important;
				}

				.works-image3 .owl-carousel .projects2-card-img {
					height: 160px !important;
				}

				.works-image3 .owl-carousel .item .projects2-card-excerpt {
					-webkit-line-clamp: 2 !important;
				}
			}
		</style>

		<script type="text/javascript">
			jQuery(document).ready(function($) {
				// Function to equalize card heights in Image3 carousel only
				function equalizeImage3CardHeights() {
					var $carousel = $('.works-image3 .owl-carousel');
					if ($carousel.length) {
						// Wait for carousel to be fully rendered
						setTimeout(function() {
							// Reset all heights first
							$carousel.find('.projects2-card').css('height', '');

							// Get all visible items (active items in current view)
							var $visibleItems = $carousel.find('.owl-item.active .projects2-card');

							if ($visibleItems.length > 1) {
								var maxHeight = 0;

								// Find the maximum height among visible cards
								$visibleItems.each(function() {
									$(this).css('height', 'auto'); // Reset individual card height
									var cardHeight = $(this).outerHeight();
									if (cardHeight > maxHeight) {
										maxHeight = cardHeight;
									}
								});

								// Apply the maximum height to all visible cards
								if (maxHeight > 0) {
									$visibleItems.css('height', maxHeight + 'px');
								}
							}
						}, 50);
					}
				}

				// Function to update center item styling
				function updateCenterItem() {
					var $carousel = $('.works-image3 .owl-carousel');
					if ($carousel.length) {
						// Remove center class from all items
						$carousel.find('.owl-item').removeClass('center');

						// Add center class to the middle active item ONLY if we have 3 items visible
						var $activeItems = $carousel.find('.owl-item.active');
						if ($activeItems.length >= 3) {
							// For 3 items, center is the 2nd item (index 1) - center mode enabled
							$activeItems.eq(1).addClass('center');
						}
						// For 2 items or 1 item, don't add center class - center mode disabled
						// This prevents weird scaling when only 2 items are visible
					}
				}

				// Debounced version of height equalization
				var heightEqualizeTimeout;

				function debouncedHeightEqualize() {
					clearTimeout(heightEqualizeTimeout);
					heightEqualizeTimeout = setTimeout(equalizeImage3CardHeights, 100);
				}

				// Initialize carousel for Image3 widget only with center mode
				var $carousel = $('.works-image3 .owl-carousel');
				if ($carousel.length) {
					// Count total items to determine if loop should be enabled
					var totalItems = $carousel.find('.item').length;

					// Center mode carousel initialization
					var $owlCarousel = $carousel.owlCarousel({
						loop: totalItems > 3, // Enable loop only if more than 3 items
						margin: 30,
						mouseDrag: true,
						autoplay: totalItems > 3, // Only autoplay if more than 3 items
						autoplayTimeout: 5000,
						dots: true,
						autoplayHoverPause: true,
						nav: false,
						navText: ['<i class="fa-solid fa-angle-left"></i>', '<i class="fa-solid fa-angle-right"></i>'],
						responsiveClass: true,
						responsive: {
							0: {
								items: 1,
								dots: true
							},
							600: {
								items: 2
							},
							1000: {
								items: 3
							}
						},
						onInitialized: function(event) {
							// Update center item styling
							setTimeout(updateCenterItem, 100);
							// Multiple attempts to ensure height equalization
							setTimeout(equalizeImage3CardHeights, 100);
							setTimeout(equalizeImage3CardHeights, 300);
							setTimeout(equalizeImage3CardHeights, 500);
						},
						onTranslated: function(event) {
							updateCenterItem();
							equalizeImage3CardHeights();
						},
						onResized: function(event) {
							updateCenterItem();
							debouncedHeightEqualize();
						}
					});

					// Equalize heights on window resize
					$(window).on('resize.image3carousel', function() {
						updateCenterItem();
						debouncedHeightEqualize();
					});

					// Force equalization after all images load
					var $images = $carousel.find('img');
					var imagesLoaded = 0;
					var totalImages = $images.length;

					if (totalImages > 0) {
						$images.on('load', function() {
							imagesLoaded++;
							if (imagesLoaded === totalImages) {
								// All images loaded, update center and equalize heights
								setTimeout(updateCenterItem, 100);
								setTimeout(equalizeImage3CardHeights, 100);
							}
						});

						// Fallback in case some images are already cached
						setTimeout(function() {
							updateCenterItem();
							equalizeImage3CardHeights();
						}, 1000);
					}

					// Additional fallback equalizations
					setTimeout(function() {
						updateCenterItem();
						equalizeImage3CardHeights();
					}, 1500);
					setTimeout(function() {
						updateCenterItem();
						equalizeImage3CardHeights();
					}, 3000);
				}
			});
		</script>
<?php
	}
}
