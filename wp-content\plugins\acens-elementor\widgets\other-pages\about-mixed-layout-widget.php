<?php
namespace BdevsElementor\Widget;

use <PERSON><PERSON>or\Controls_Manager;
use Elementor\Group_Control_Typography;
use Elementor\Scheme_Typography;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;
use Elementor\Group_Control_Background;
use Elementor\Group_Control_Image_Size;
use <PERSON>ementor\Repeater;
use Elementor\Utils;

/**
 * About Mixed Layout Widget
 *
 * Enhanced Elementor widget for flexible content and image layouts
 * Based on the original About Services Pages widget with extended functionality
 *
 * @since 1.0.0
 */
class BdevsAboutMixedLayout extends \Elementor\Widget_Base {

	/**
	 * Get widget name.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Widget name.
	 */
	public function get_name() {
		return 'bdevs-about-mixed-layout';
	}

	/**
	 * Get widget title.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Widget title.
	 */
	public function get_title() {
		return __( 'About Mixed Layout', 'bdevs-elementor' );
	}

	/**
	 * Get widget icon.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Widget icon.
	 */
	public function get_icon() {
		return 'eicon-layout-settings';
	}

	/**
	 * Get widget categories.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Widget categories.
	 */
	public function get_categories() {
		return [ 'other-pages-elementor' ];
	}

	/**
	 * Get widget keywords.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Widget keywords.
	 */
	public function get_keywords() {
		return [ 'about', 'mixed', 'layout', 'services', 'images', 'flexible' ];
	}

	/**
	 * Get script dependencies.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Script dependencies.
	 */
	public function get_script_depends() {
		return [ 'bdevs-elementor' ];
	}

	/**
	 * Register widget controls.
	 *
	 * @since 1.0.0
	 * @access protected
	 */
	protected function _register_controls() {
		
		// Section Header Controls
		$this->start_controls_section(
			'section_header',
			[
				'label' => esc_html__( 'Section Header', 'bdevs-elementor' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			]
		);

		$this->add_control(
			'section_subheading',
			[
				'label'       => __( 'Section Subheading', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __( 'Enter section subheading', 'bdevs-elementor' ),
				'default'     => __( 'Where We', 'bdevs-elementor' ),
				'label_block' => true,
			]
		);

		$this->add_control(
			'section_heading',
			[
				'label'       => __( 'Section Heading', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __( 'Enter section heading', 'bdevs-elementor' ),
				'default'     => __( 'Work From', 'bdevs-elementor' ),
				'label_block' => true,
			]
		);

		$this->add_control(
			'section_description',
			[
				'label'       => __( 'Section Description', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXTAREA,
				'placeholder' => __( 'Enter section description (optional)', 'bdevs-elementor' ),
				'default'     => __( 'From different cities and backgrounds, we collaborate seamlessly to deliver world-class digital services.', 'bdevs-elementor' ),
				'label_block' => true,
				'rows'        => 3,
			]
		);

		// HTML Tag Controls for Section Header
		$this->add_control(
			'section_header_html_tags_heading',
			[
				'label'     => esc_html__( 'HTML Tag Options', 'bdevs-elementor' ),
				'type'      => Controls_Manager::HEADING,
				'separator' => 'before',
			]
		);

		$this->add_control(
			'section_subheading_tag',
			[
				'label'   => esc_html__( 'Subheading HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h6',
				'options' => [
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for semantic structure and SEO. Default: H6', 'bdevs-elementor' ),
			]
		);

		$this->add_control(
			'section_heading_tag',
			[
				'label'   => esc_html__( 'Main Heading HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h1',
				'options' => [
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for semantic structure and SEO. Default: H1', 'bdevs-elementor' ),
			]
		);

		$this->add_control(
			'section_description_tag',
			[
				'label'   => esc_html__( 'Description HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'p',
				'options' => [
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for semantic structure and SEO. Default: P', 'bdevs-elementor' ),
			]
		);

		$this->end_controls_section();

		// Featured Images Section
		$this->start_controls_section(
			'section_featured_images',
			[
				'label' => esc_html__( 'Featured Images', 'bdevs-elementor' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			]
		);

		$this->add_control(
			'featured_image_1',
			[
				'label'       => esc_html__( 'Featured Image 1', 'bdevs-elementor' ),
				'type'        => Controls_Manager::MEDIA,
				'dynamic'     => [ 'active' => true ],
				'default'     => [
					'url' => Utils::get_placeholder_image_src(),
				],
				'label_block' => true,
				'description' => esc_html__( 'Upload the first featured image (recommended: 800x600px)', 'bdevs-elementor' ),
			]
		);

		$this->add_control(
			'featured_title_1',
			[
				'label'       => __( 'Featured Title 1', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __( 'Enter title for first image', 'bdevs-elementor' ),
				'default'     => __( 'Offices Across the Philippines', 'bdevs-elementor' ),
				'label_block' => true,
			]
		);

		$this->add_control(
			'featured_subtitle_1',
			[
				'label'       => __( 'Featured Subtitle 1', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __( 'Enter subtitle for first image', 'bdevs-elementor' ),
				'default'     => __( 'Davao City, Cotabato City, Siquijor City, Philippines', 'bdevs-elementor' ),
				'label_block' => true,
			]
		);

		$this->add_control(
			'featured_image_2',
			[
				'label'       => esc_html__( 'Featured Image 2', 'bdevs-elementor' ),
				'type'        => Controls_Manager::MEDIA,
				'dynamic'     => [ 'active' => true ],
				'default'     => [
					'url' => Utils::get_placeholder_image_src(),
				],
				'label_block' => true,
				'description' => esc_html__( 'Upload the second featured image (recommended: 400x600px)', 'bdevs-elementor' ),
			]
		);

		$this->add_control(
			'featured_title_2',
			[
				'label'       => __( 'Featured Title 2', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __( 'Enter title for second image', 'bdevs-elementor' ),
				'default'     => __( 'Web Development', 'bdevs-elementor' ),
				'label_block' => true,
			]
		);

		$this->add_control(
			'featured_subtitle_2',
			[
				'label'       => __( 'Featured Subtitle 2', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __( 'Enter subtitle for second image', 'bdevs-elementor' ),
				'default'     => __( 'Baliuag City, Philippines', 'bdevs-elementor' ),
				'label_block' => true,
			]
		);

		// HTML Tag Controls for Featured Images
		$this->add_control(
			'featured_images_html_tags_heading',
			[
				'label'     => esc_html__( 'Featured Images HTML Tags', 'bdevs-elementor' ),
				'type'      => Controls_Manager::HEADING,
				'separator' => 'before',
			]
		);

		$this->add_control(
			'featured_title_tag',
			[
				'label'   => esc_html__( 'Featured Titles HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h4',
				'options' => [
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for featured image titles. Default: H4', 'bdevs-elementor' ),
			]
		);

		$this->add_control(
			'featured_subtitle_tag',
			[
				'label'   => esc_html__( 'Featured Subtitles HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h6',
				'options' => [
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for featured image subtitles. Default: H6', 'bdevs-elementor' ),
			]
		);

		$this->end_controls_section();

		// Content Blocks Section
		$this->start_controls_section(
			'section_content_blocks',
			[
				'label' => esc_html__( 'Content Blocks', 'bdevs-elementor' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			]
		);

		$repeater = new Repeater();

		$repeater->add_control(
			'block_type',
			[
				'label'   => esc_html__( 'Block Type', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'text',
				'options' => [
					'text'  => esc_html__( 'Text Content', 'bdevs-elementor' ),
					'image' => esc_html__( 'Image Content', 'bdevs-elementor' ),
				],
			]
		);

		$repeater->add_control(
			'heading',
			[
				'label'       => esc_html__( 'Heading', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'dynamic'     => [ 'active' => true ],
				'default'     => esc_html__( 'What we do', 'bdevs-elementor' ),
				'label_block' => true,
			]
		);

		$repeater->add_control(
			'content',
			[
				'label'       => esc_html__( 'Content', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXTAREA,
				'dynamic'     => [ 'active' => true ],
				'default'     => esc_html__( 'We create and maintain websites, design graphics, manage social media, and provide virtual assistance. Our remote-first team in the Philippines combines creativity and technical expertise to help businesses establish a strong online presence and grow their digital footprint.', 'bdevs-elementor' ),
				'label_block' => true,
				'rows'        => 4,
			]
		);

		$repeater->add_control(
			'link_url',
			[
				'label'       => esc_html__( 'Link URL', 'bdevs-elementor' ),
				'type'        => Controls_Manager::URL,
				'dynamic'     => [ 'active' => true ],
				'placeholder' => esc_html__( 'https://your-link.com', 'bdevs-elementor' ),
				'label_block' => true,
			]
		);

		$repeater->add_control(
			'block_image',
			[
				'label'     => esc_html__( 'Image', 'bdevs-elementor' ),
				'type'      => Controls_Manager::MEDIA,
				'dynamic'   => [ 'active' => true ],
				'default'   => [
					'url' => Utils::get_placeholder_image_src(),
				],
				'condition' => [
					'block_type' => 'image',
				],
			]
		);

		// Content Block HTML Tag Controls
		$repeater->add_control(
			'content_html_tags_heading',
			[
				'label'     => esc_html__( 'Content HTML Tags', 'bdevs-elementor' ),
				'type'      => Controls_Manager::HEADING,
				'separator' => 'before',
			]
		);

		$repeater->add_control(
			'content_heading_tag',
			[
				'label'   => esc_html__( 'Heading HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h4',
				'options' => [
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for content block heading. Default: H4', 'bdevs-elementor' ),
			]
		);

		$repeater->add_control(
			'content_text_tag',
			[
				'label'   => esc_html__( 'Content Text HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'p',
				'options' => [
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for content block text. Default: P', 'bdevs-elementor' ),
			]
		);

		$this->add_control(
			'content_blocks',
			[
				'label'       => esc_html__( 'Content Blocks', 'bdevs-elementor' ),
				'type'        => Controls_Manager::REPEATER,
				'fields'      => $repeater->get_controls(),
				'default'     => [
					[
						'block_type' => 'text',
						'heading'    => esc_html__( 'What we do', 'bdevs-elementor' ),
						'content'    => esc_html__( 'We create and maintain websites, design graphics, manage social media, and provide virtual assistance.', 'bdevs-elementor' ),
					],
					[
						'block_type' => 'text',
						'heading'    => esc_html__( 'How we work', 'bdevs-elementor' ),
						'content'    => esc_html__( 'Our process is straightforward and collaborative. We work closely with you from initial consultation through project completion.', 'bdevs-elementor' ),
					],
					[
						'block_type' => 'text',
						'heading'    => esc_html__( 'What Drives Us', 'bdevs-elementor' ),
						'content'    => esc_html__( 'We are more than just a digital agency. We believe in honesty and are here to make a genuine impact.', 'bdevs-elementor' ),
					],
				],
				'title_field' => '{{{ heading }}}',
			]
		);

		$this->end_controls_section();

		// Layout Settings Section
		$this->start_controls_section(
			'section_layout_settings',
			[
				'label' => esc_html__( 'Layout Settings', 'bdevs-elementor' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			]
		);

		$this->add_responsive_control(
			'content_alignment',
			[
				'label'   => esc_html__( 'Content Alignment', 'bdevs-elementor' ),
				'type'    => Controls_Manager::CHOOSE,
				'options' => [
					'left' => [
						'title' => esc_html__( 'Left', 'bdevs-elementor' ),
						'icon'  => 'eicon-text-align-left',
					],
					'center' => [
						'title' => esc_html__( 'Center', 'bdevs-elementor' ),
						'icon'  => 'eicon-text-align-center',
					],
					'right' => [
						'title' => esc_html__( 'Right', 'bdevs-elementor' ),
						'icon'  => 'eicon-text-align-right',
					],
				],
				'default' => 'left',
				'selectors' => [
					'{{WRAPPER}} .about-mixed-layout .content-block' => 'text-align: {{VALUE}};',
				],
			]
		);

		$this->add_control(
			'content_blocks_per_row',
			[
				'label'   => esc_html__( 'Content Blocks Per Row', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => '3',
				'options' => [
					'1' => esc_html__( '1 Column', 'bdevs-elementor' ),
					'2' => esc_html__( '2 Columns', 'bdevs-elementor' ),
					'3' => esc_html__( '3 Columns', 'bdevs-elementor' ),
					'4' => esc_html__( '4 Columns', 'bdevs-elementor' ),
				],
			]
		);

		$this->end_controls_section();
	}

	/**
	 * Render widget output on the frontend.
	 *
	 * @since 1.0.0
	 * @access public
	 */
	public function render() {
		$settings = $this->get_settings_for_display();

		// Get HTML tags for section header with fallback to defaults
		$section_subheading_tag = !empty($settings['section_subheading_tag']) ? $settings['section_subheading_tag'] : 'h6';
		$section_heading_tag = !empty($settings['section_heading_tag']) ? $settings['section_heading_tag'] : 'h1';
		$section_description_tag = !empty($settings['section_description_tag']) ? $settings['section_description_tag'] : 'p';

		// Get HTML tags for featured images with fallback to defaults
		$featured_title_tag = !empty($settings['featured_title_tag']) ? $settings['featured_title_tag'] : 'h4';
		$featured_subtitle_tag = !empty($settings['featured_subtitle_tag']) ? $settings['featured_subtitle_tag'] : 'h6';
		?>
		<section class="about-mixed-layout section-padding">
			<div class="container">
				<!-- Section Header -->
				<?php if ( !empty($settings['section_subheading']) || !empty($settings['section_heading']) || !empty($settings['section_description']) ) : ?>
				<div class="row">
					<div class="col-md-12">
						<div class="section-header text-center mb-60">
							<?php if ( !empty($settings['section_subheading']) ) : ?>
							<<?php echo esc_attr($section_subheading_tag); ?> class="section-header-subheading"><?php echo wp_kses_post($settings['section_subheading']); ?></<?php echo esc_attr($section_subheading_tag); ?>>
							<?php endif; ?>
							<?php if ( !empty($settings['section_heading']) ) : ?>
							<<?php echo esc_attr($section_heading_tag); ?> class="section-header-heading"><?php echo wp_kses_post($settings['section_heading']); ?></<?php echo esc_attr($section_heading_tag); ?>>
							<?php endif; ?>
							<?php if ( !empty($settings['section_description']) ) : ?>
							<<?php echo esc_attr($section_description_tag); ?> class="section-header-description"><?php echo wp_kses_post($settings['section_description']); ?></<?php echo esc_attr($section_description_tag); ?>>
							<?php endif; ?>
						</div>
					</div>
				</div>
				<?php endif; ?>

				<!-- Featured Images Row -->
				<div class="row mb-60">
					<?php if ( !empty($settings['featured_image_1']['url']) ) : ?>
					<div class="col-md-8">
						<div class="item">
							<div class="wrap">
								<div class="img">
									<img src="<?php echo esc_url($settings['featured_image_1']['url']); ?>" class="img-fluid" alt="<?php echo esc_attr($settings['featured_title_1']); ?>">
								</div>
								<div class="title">
									<?php if ( !empty($settings['featured_title_1']) ) : ?>
									<<?php echo esc_attr($featured_title_tag); ?> class="about-item-title"><?php echo wp_kses_post($settings['featured_title_1']); ?></<?php echo esc_attr($featured_title_tag); ?>>
									<?php endif; ?>
									<?php if ( !empty($settings['featured_subtitle_1']) ) : ?>
									<<?php echo esc_attr($featured_subtitle_tag); ?> class="about-item-subtitle"><?php echo wp_kses_post($settings['featured_subtitle_1']); ?></<?php echo esc_attr($featured_subtitle_tag); ?>>
									<?php endif; ?>
								</div>
							</div>
						</div>
					</div>
					<?php endif; ?>

					<?php if ( !empty($settings['featured_image_2']['url']) ) : ?>
					<div class="col-md-4">
						<div class="item">
							<div class="wrap">
								<div class="img">
									<img src="<?php echo esc_url($settings['featured_image_2']['url']); ?>" class="img-fluid" alt="<?php echo esc_attr($settings['featured_title_2']); ?>">
								</div>
								<div class="title">
									<?php if ( !empty($settings['featured_title_2']) ) : ?>
									<<?php echo esc_attr($featured_title_tag); ?> class="about-item-title"><?php echo wp_kses_post($settings['featured_title_2']); ?></<?php echo esc_attr($featured_title_tag); ?>>
									<?php endif; ?>
									<?php if ( !empty($settings['featured_subtitle_2']) ) : ?>
									<<?php echo esc_attr($featured_subtitle_tag); ?> class="about-item-subtitle"><?php echo wp_kses_post($settings['featured_subtitle_2']); ?></<?php echo esc_attr($featured_subtitle_tag); ?>>
									<?php endif; ?>
								</div>
							</div>
						</div>
					</div>
					<?php endif; ?>
				</div>

				<!-- Content Blocks -->
				<?php if ( !empty($settings['content_blocks']) ) : ?>
				<div class="about-box">
					<div class="row">
						<?php
						$columns_class = 'col-md-' . (12 / intval($settings['content_blocks_per_row']));
						foreach ( $settings['content_blocks'] as $index => $block ) :
							// Get HTML tags for content block with fallback to defaults
							$content_heading_tag = !empty($block['content_heading_tag']) ? $block['content_heading_tag'] : 'h4';
							$content_text_tag = !empty($block['content_text_tag']) ? $block['content_text_tag'] : 'p';
						?>
						<div class="<?php echo esc_attr($columns_class); ?>">
							<div class="item">
								<div class="wrap">
									<?php if ( $block['block_type'] === 'image' && !empty($block['block_image']['url']) ) : ?>
									<div class="img">
										<img src="<?php echo esc_url($block['block_image']['url']); ?>" class="img-fluid" alt="<?php echo esc_attr($block['heading']); ?>">
									</div>
									<?php endif; ?>

									<div class="con content-block">
										<?php if ( !empty($block['heading']) ) : ?>
											<?php if ( !empty($block['link_url']['url']) ) : ?>
												<a href="<?php echo esc_url($block['link_url']['url']); ?>"
												   <?php echo !empty($block['link_url']['is_external']) ? 'target="_blank"' : ''; ?>
												   <?php echo !empty($block['link_url']['nofollow']) ? 'rel="nofollow"' : ''; ?>>
													<<?php echo esc_attr($content_heading_tag); ?> class="about-content-heading"><?php echo wp_kses_post($block['heading']); ?></<?php echo esc_attr($content_heading_tag); ?>>
												</a>
											<?php else : ?>
												<<?php echo esc_attr($content_heading_tag); ?> class="about-content-heading"><?php echo wp_kses_post($block['heading']); ?></<?php echo esc_attr($content_heading_tag); ?>>
											<?php endif; ?>
										<?php endif; ?>

										<?php if ( !empty($block['content']) ) : ?>
										<<?php echo esc_attr($content_text_tag); ?> class="section-header-description"><?php echo wp_kses_post($block['content']); ?></<?php echo esc_attr($content_text_tag); ?>>
										<?php endif; ?>
										
										<?php if ( !empty($block['link_url']['url']) ) : ?>
										<div class="icon-2">
											<a href="<?php echo esc_url($block['link_url']['url']); ?>"
											   <?php echo !empty($block['link_url']['is_external']) ? 'target="_blank"' : ''; ?>
											   <?php echo !empty($block['link_url']['nofollow']) ? 'rel="nofollow"' : ''; ?>>
												<span class="fa-sharp fa-light fa-arrow-right"></span>
											</a>
										</div>
										<?php endif; ?>
									</div>
								</div>
							</div>
						</div>
						<?php endforeach; ?>
					</div>
				</div>
				<?php endif; ?>
			</div>
		</section>
		<?php
	}
}
