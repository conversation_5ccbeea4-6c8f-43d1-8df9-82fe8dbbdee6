<?php
namespace BdevsElementor\Widget;

use <PERSON><PERSON>or\Controls_Manager;
use <PERSON>ementor\Group_Control_Typography;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;
use Elementor\Group_Control_Background;
use Elementor\Group_Control_Image_Size;
use <PERSON>ementor\Repeater;
use <PERSON><PERSON><PERSON>\Utils;

/**
 * About Services Mixed Layout Widget
 *
 * Enhanced Elementor widget for flexible content and image layouts
 * Extends the original About Services Pages widget with advanced functionality
 *
 * @since 1.0.0
 */
class BdevsAboutServicesMixedLayout extends \Elementor\Widget_Base {

	/**
	 * Get widget name.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Widget name.
	 */
	public function get_name() {
		return 'bdevs-about-services-mixed-layout';
	}

	/**
	 * Get widget title.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Widget title.
	 */
	public function get_title() {
		return __( 'About Services Mixed Layout', 'bdevs-elementor' );
	}

	/**
	 * Get widget icon.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return string Widget icon.
	 */
	public function get_icon() {
		return 'eicon-layout-settings';
	}

	/**
	 * Get widget categories.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Widget categories.
	 */
	public function get_categories() {
		return [ 'other-pages-elementor' ];
	}

	/**
	 * Get widget keywords.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Widget keywords.
	 */
	public function get_keywords() {
		return [ 'about', 'services', 'mixed', 'layout', 'flexible', 'content' ];
	}

	/**
	 * Get script dependencies.
	 *
	 * @since 1.0.0
	 * @access public
	 * @return array Script dependencies.
	 */
	public function get_script_depends() {
		return [ 'bdevs-elementor' ];
	}

	/**
	 * Register widget controls.
	 *
	 * @since 1.0.0
	 * @access protected
	 */
	protected function _register_controls() {
		
		// Section Header Controls
		$this->start_controls_section(
			'section_header',
			[
				'label' => esc_html__( 'Section Header', 'bdevs-elementor' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			]
		);

		$this->add_control(
			'section_subheading',
			[
				'label'       => __( 'Section Subheading', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __( 'Enter section subheading', 'bdevs-elementor' ),
				'default'     => __( 'Where We', 'bdevs-elementor' ),
				'label_block' => true,
				'dynamic'     => [ 'active' => true ],
			]
		);

		$this->add_control(
			'section_heading',
			[
				'label'       => __( 'Section Heading', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'placeholder' => __( 'Enter section heading', 'bdevs-elementor' ),
				'default'     => __( 'Work From', 'bdevs-elementor' ),
				'label_block' => true,
				'dynamic'     => [ 'active' => true ],
			]
		);

		$this->add_control(
			'section_description',
			[
				'label'       => __( 'Section Description', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXTAREA,
				'placeholder' => __( 'Enter section description (optional)', 'bdevs-elementor' ),
				'default'     => __( 'From different cities and backgrounds, we collaborate seamlessly to deliver world-class digital services.', 'bdevs-elementor' ),
				'label_block' => true,
				'rows'        => 3,
				'dynamic'     => [ 'active' => true ],
			]
		);

		// HTML Tag Controls for Section Header
		$this->add_control(
			'section_header_html_tags_heading',
			[
				'label'     => esc_html__( 'HTML Tag Options', 'bdevs-elementor' ),
				'type'      => Controls_Manager::HEADING,
				'separator' => 'before',
			]
		);

		$this->add_control(
			'section_subheading_tag',
			[
				'label'   => esc_html__( 'Subheading HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h6',
				'options' => [
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for semantic structure and SEO. Default: H6', 'bdevs-elementor' ),
			]
		);

		$this->add_control(
			'section_heading_tag',
			[
				'label'   => esc_html__( 'Main Heading HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h1',
				'options' => [
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for semantic structure and SEO. Default: H1', 'bdevs-elementor' ),
			]
		);

		$this->add_control(
			'section_description_tag',
			[
				'label'   => esc_html__( 'Description HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'p',
				'options' => [
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for semantic structure and SEO. Default: P', 'bdevs-elementor' ),
			]
		);

		$this->end_controls_section();

		// Flexible Layout Blocks Section
		$this->start_controls_section(
			'section_layout_blocks',
			[
				'label' => esc_html__( 'Layout Blocks', 'bdevs-elementor' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			]
		);

		$this->add_control(
			'layout_blocks_description',
			[
				'type'            => Controls_Manager::RAW_HTML,
				'raw'             => esc_html__( 'Create flexible layouts by mixing image and content blocks. Each block can have different column widths (1-12 columns). Blocks automatically wrap to new rows when totaling more than 12 columns.', 'bdevs-elementor' ),
				'content_classes' => 'elementor-panel-alert elementor-panel-alert-info',
			]
		);

		$repeater = new Repeater();

		$repeater->add_control(
			'block_type',
			[
				'label'   => esc_html__( 'Block Type', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'content',
				'options' => [
					'content' => esc_html__( 'Content Block', 'bdevs-elementor' ),
					'image'   => esc_html__( 'Image Block', 'bdevs-elementor' ),
				],
			]
		);

		$repeater->add_control(
			'column_width',
			[
				'label'   => esc_html__( 'Column Width (Desktop)', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => '4',
				'options' => [
					'1'  => esc_html__( '1 Column (8.33%)', 'bdevs-elementor' ),
					'2'  => esc_html__( '2 Columns (16.67%)', 'bdevs-elementor' ),
					'3'  => esc_html__( '3 Columns (25%)', 'bdevs-elementor' ),
					'4'  => esc_html__( '4 Columns (33.33%)', 'bdevs-elementor' ),
					'6'  => esc_html__( '6 Columns (50%)', 'bdevs-elementor' ),
					'8'  => esc_html__( '8 Columns (66.67%)', 'bdevs-elementor' ),
					'12' => esc_html__( '12 Columns (100%)', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the width for this block on desktop devices', 'bdevs-elementor' ),
			]
		);

		$repeater->add_control(
			'column_width_tablet',
			[
				'label'   => esc_html__( 'Column Width (Tablet)', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => '6',
				'options' => [
					'1'  => esc_html__( '1 Column (8.33%)', 'bdevs-elementor' ),
					'2'  => esc_html__( '2 Columns (16.67%)', 'bdevs-elementor' ),
					'3'  => esc_html__( '3 Columns (25%)', 'bdevs-elementor' ),
					'4'  => esc_html__( '4 Columns (33.33%)', 'bdevs-elementor' ),
					'6'  => esc_html__( '6 Columns (50%)', 'bdevs-elementor' ),
					'8'  => esc_html__( '8 Columns (66.67%)', 'bdevs-elementor' ),
					'12' => esc_html__( '12 Columns (100%)', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the width for this block on tablet devices', 'bdevs-elementor' ),
			]
		);

		$repeater->add_control(
			'column_width_mobile',
			[
				'label'   => esc_html__( 'Column Width (Mobile)', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => '12',
				'options' => [
					'6'  => esc_html__( '6 Columns (50%)', 'bdevs-elementor' ),
					'12' => esc_html__( '12 Columns (100%)', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the width for this block on mobile devices', 'bdevs-elementor' ),
			]
		);

		// Force Row Break - Desktop
		$repeater->add_control(
			'force_row_break_desktop',
			[
				'label'        => esc_html__( 'Force Row Break (Desktop)', 'bdevs-elementor' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => esc_html__( 'Yes', 'bdevs-elementor' ),
				'label_off'    => esc_html__( 'No', 'bdevs-elementor' ),
				'return_value' => 'yes',
				'default'      => 'no',
				'description'  => esc_html__( 'Force this block to start a new row on desktop devices (≥992px)', 'bdevs-elementor' ),
			]
		);

		// Force Row Break - Tablet
		$repeater->add_control(
			'force_row_break_tablet',
			[
				'label'        => esc_html__( 'Force Row Break (Tablet)', 'bdevs-elementor' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => esc_html__( 'Yes', 'bdevs-elementor' ),
				'label_off'    => esc_html__( 'No', 'bdevs-elementor' ),
				'return_value' => 'yes',
				'default'      => 'no',
				'description'  => esc_html__( 'Force this block to start a new row on tablet devices (768px-991px)', 'bdevs-elementor' ),
			]
		);

		// Force Row Break - Mobile
		$repeater->add_control(
			'force_row_break_mobile',
			[
				'label'        => esc_html__( 'Force Row Break (Mobile)', 'bdevs-elementor' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => esc_html__( 'Yes', 'bdevs-elementor' ),
				'label_off'    => esc_html__( 'No', 'bdevs-elementor' ),
				'return_value' => 'yes',
				'default'      => 'no',
				'description'  => esc_html__( 'Force this block to start a new row on mobile devices (≤767px)', 'bdevs-elementor' ),
				'separator'    => 'after',
			]
		);

		// Backward compatibility: Keep the old force_row_break control for existing widgets
		$repeater->add_control(
			'force_row_break',
			[
				'label'        => esc_html__( 'Force Row Break (Legacy)', 'bdevs-elementor' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => esc_html__( 'Yes', 'bdevs-elementor' ),
				'label_off'    => esc_html__( 'No', 'bdevs-elementor' ),
				'return_value' => 'yes',
				'default'      => 'no',
				'description'  => esc_html__( 'Legacy control - use responsive controls above instead', 'bdevs-elementor' ),
				'condition'    => [
					'force_row_break_desktop' => '',
					'force_row_break_tablet'  => '',
					'force_row_break_mobile'  => '',
				],
			]
		);

		// Content Block Controls
		$repeater->add_control(
			'content_heading',
			[
				'label'       => esc_html__( 'Heading', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'dynamic'     => [ 'active' => true ],
				'default'     => esc_html__( 'What we do', 'bdevs-elementor' ),
				'label_block' => true,
				'condition'   => [
					'block_type' => 'content',
				],
			]
		);

		$repeater->add_control(
			'content_text',
			[
				'label'       => esc_html__( 'Content', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXTAREA,
				'dynamic'     => [ 'active' => true ],
				'default'     => esc_html__( 'We create and maintain websites, design graphics, manage social media, and provide virtual assistance. Our remote-first team combines creativity and technical expertise.', 'bdevs-elementor' ),
				'label_block' => true,
				'rows'        => 4,
				'condition'   => [
					'block_type' => 'content',
				],
			]
		);

		$repeater->add_control(
			'content_link',
			[
				'label'       => esc_html__( 'Link', 'bdevs-elementor' ),
				'type'        => Controls_Manager::URL,
				'dynamic'     => [ 'active' => true ],
				'placeholder' => esc_html__( 'https://your-link.com', 'bdevs-elementor' ),
				'label_block' => true,
				'condition'   => [
					'block_type' => 'content',
				],
			]
		);

		// Content Block HTML Tag Controls
		$repeater->add_control(
			'content_html_tags_heading',
			[
				'label'     => esc_html__( 'Content HTML Tags', 'bdevs-elementor' ),
				'type'      => Controls_Manager::HEADING,
				'separator' => 'before',
				'condition' => [
					'block_type' => 'content',
				],
			]
		);

		$repeater->add_control(
			'content_heading_tag',
			[
				'label'   => esc_html__( 'Heading HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h4',
				'options' => [
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for content block heading. Default: H4', 'bdevs-elementor' ),
				'condition' => [
					'block_type' => 'content',
				],
			]
		);

		$repeater->add_control(
			'content_text_tag',
			[
				'label'   => esc_html__( 'Content Text HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'p',
				'options' => [
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for content block text. Default: P', 'bdevs-elementor' ),
				'condition' => [
					'block_type' => 'content',
				],
			]
		);

		// Image Block Controls
		$repeater->add_control(
			'image_block_image',
			[
				'label'     => esc_html__( 'Image', 'bdevs-elementor' ),
				'type'      => Controls_Manager::MEDIA,
				'dynamic'   => [ 'active' => true ],
				'default'   => [
					'url' => Utils::get_placeholder_image_src(),
				],
				'condition' => [
					'block_type' => 'image',
				],
			]
		);

		$repeater->add_group_control(
			Group_Control_Image_Size::get_type(),
			[
				'name'      => 'image_block_size',
				'default'   => 'large',
				'separator' => 'none',
				'condition' => [
					'block_type'              => 'image',
					'image_block_image[url]!' => '',
				],
			]
		);

		$repeater->add_control(
			'image_title',
			[
				'label'       => esc_html__( 'Image Title', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'dynamic'     => [ 'active' => true ],
				'default'     => esc_html__( 'Offices Across the Philippines', 'bdevs-elementor' ),
				'label_block' => true,
				'condition'   => [
					'block_type' => 'image',
				],
			]
		);

		$repeater->add_control(
			'image_subtitle',
			[
				'label'       => esc_html__( 'Image Subtitle', 'bdevs-elementor' ),
				'type'        => Controls_Manager::TEXT,
				'dynamic'     => [ 'active' => true ],
				'default'     => esc_html__( 'Davao City, Cotabato City, Siquijor City, Philippines', 'bdevs-elementor' ),
				'label_block' => true,
				'condition'   => [
					'block_type' => 'image',
				],
			]
		);

		$repeater->add_control(
			'image_link',
			[
				'label'       => esc_html__( 'Image Link', 'bdevs-elementor' ),
				'type'        => Controls_Manager::URL,
				'dynamic'     => [ 'active' => true ],
				'placeholder' => esc_html__( 'https://your-link.com', 'bdevs-elementor' ),
				'label_block' => true,
				'condition'   => [
					'block_type' => 'image',
				],
			]
		);

		// Image Block HTML Tag Controls
		$repeater->add_control(
			'image_html_tags_heading',
			[
				'label'     => esc_html__( 'Image Block HTML Tags', 'bdevs-elementor' ),
				'type'      => Controls_Manager::HEADING,
				'separator' => 'before',
				'condition' => [
					'block_type' => 'image',
				],
			]
		);

		$repeater->add_control(
			'image_title_tag',
			[
				'label'   => esc_html__( 'Image Title HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h4',
				'options' => [
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for image block title. Default: H4', 'bdevs-elementor' ),
				'condition' => [
					'block_type' => 'image',
				],
			]
		);

		$repeater->add_control(
			'image_subtitle_tag',
			[
				'label'   => esc_html__( 'Image Subtitle HTML Tag', 'bdevs-elementor' ),
				'type'    => Controls_Manager::SELECT,
				'default' => 'h6',
				'options' => [
					'h1'   => esc_html__( 'H1', 'bdevs-elementor' ),
					'h2'   => esc_html__( 'H2', 'bdevs-elementor' ),
					'h3'   => esc_html__( 'H3', 'bdevs-elementor' ),
					'h4'   => esc_html__( 'H4', 'bdevs-elementor' ),
					'h5'   => esc_html__( 'H5', 'bdevs-elementor' ),
					'h6'   => esc_html__( 'H6', 'bdevs-elementor' ),
					'div'  => esc_html__( 'DIV', 'bdevs-elementor' ),
					'span' => esc_html__( 'SPAN', 'bdevs-elementor' ),
					'p'    => esc_html__( 'P', 'bdevs-elementor' ),
				],
				'description' => esc_html__( 'Choose the HTML tag for image block subtitle. Default: H6', 'bdevs-elementor' ),
				'condition' => [
					'block_type' => 'image',
				],
			]
		);

		$this->add_control(
			'layout_blocks',
			[
				'label'       => esc_html__( 'Layout Blocks', 'bdevs-elementor' ),
				'type'        => Controls_Manager::REPEATER,
				'fields'      => $repeater->get_controls(),
				'default'     => [
					[
						'block_type'      => 'image',
						'column_width'    => '8',
						'column_width_tablet' => '12',
						'column_width_mobile' => '12',
						'force_row_break_desktop' => 'no',
						'force_row_break_tablet'  => 'no',
						'force_row_break_mobile'  => 'no',
						'image_title'     => esc_html__( 'Offices Across the Philippines', 'bdevs-elementor' ),
						'image_subtitle'  => esc_html__( 'Davao City, Cotabato City, Siquijor City, Philippines', 'bdevs-elementor' ),
					],
					[
						'block_type'      => 'image',
						'column_width'    => '4',
						'column_width_tablet' => '12',
						'column_width_mobile' => '12',
						'force_row_break_desktop' => 'no',
						'force_row_break_tablet'  => 'yes', // Force new row on tablet for better layout
						'force_row_break_mobile'  => 'no',
						'image_title'     => esc_html__( 'Web Development', 'bdevs-elementor' ),
						'image_subtitle'  => esc_html__( 'Baliuag City, Philippines', 'bdevs-elementor' ),
					],
					[
						'block_type'      => 'content',
						'column_width'    => '4',
						'column_width_tablet' => '6',
						'column_width_mobile' => '12',
						'force_row_break_desktop' => 'yes', // Force new row on desktop for content section
						'force_row_break_tablet'  => 'yes', // Force new row on tablet for content section
						'force_row_break_mobile'  => 'no',
						'content_heading' => esc_html__( 'What we do', 'bdevs-elementor' ),
						'content_text'    => esc_html__( 'We create and maintain websites, design graphics, manage social media, and provide virtual assistance. Our remote-first team combines creativity and technical expertise.', 'bdevs-elementor' ),
					],
					[
						'block_type'      => 'content',
						'column_width'    => '4',
						'column_width_tablet' => '6',
						'column_width_mobile' => '12',
						'force_row_break_desktop' => 'no',
						'force_row_break_tablet'  => 'no',
						'force_row_break_mobile'  => 'no',
						'content_heading' => esc_html__( 'How we work', 'bdevs-elementor' ),
						'content_text'    => esc_html__( 'Our process is straightforward and collaborative. We work closely with you from initial consultation through project completion.', 'bdevs-elementor' ),
					],
					[
						'block_type'      => 'content',
						'column_width'    => '4',
						'column_width_tablet' => '12',
						'column_width_mobile' => '12',
						'force_row_break_desktop' => 'no',
						'force_row_break_tablet'  => 'yes', // Force new row on tablet for full-width block
						'force_row_break_mobile'  => 'no',
						'content_heading' => esc_html__( 'What Drives Us', 'bdevs-elementor' ),
						'content_text'    => esc_html__( 'We are more than just a digital agency. We believe in honesty and are here to make a genuine impact.', 'bdevs-elementor' ),
					],
				],
				'title_field' => '{{{ block_type === "image" ? image_title : content_heading }}}',
			]
		);

		$this->end_controls_section();

		// Layout Settings Section
		$this->start_controls_section(
			'section_layout_settings',
			[
				'label' => esc_html__( 'Layout Settings', 'bdevs-elementor' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			]
		);

		$this->add_responsive_control(
			'content_alignment',
			[
				'label'   => esc_html__( 'Content Alignment', 'bdevs-elementor' ),
				'type'    => Controls_Manager::CHOOSE,
				'options' => [
					'left' => [
						'title' => esc_html__( 'Left', 'bdevs-elementor' ),
						'icon'  => 'eicon-text-align-left',
					],
					'center' => [
						'title' => esc_html__( 'Center', 'bdevs-elementor' ),
						'icon'  => 'eicon-text-align-center',
					],
					'right' => [
						'title' => esc_html__( 'Right', 'bdevs-elementor' ),
						'icon'  => 'eicon-text-align-right',
					],
				],
				'default' => 'left',
				'selectors' => [
					'{{WRAPPER}} .about-services-mixed-layout .layout-block' => 'text-align: {{VALUE}};',
					'{{WRAPPER}} .about-services-mixed-layout .section-header' => 'text-align: {{VALUE}};',
				],
			]
		);

		$this->add_responsive_control(
			'block_spacing',
			[
				'label'      => esc_html__( 'Block Spacing', 'bdevs-elementor' ),
				'type'       => Controls_Manager::SLIDER,
				'size_units' => [ 'px', 'em', 'rem' ],
				'range'      => [
					'px' => [
						'min' => 0,
						'max' => 100,
					],
				],
				'default'    => [
					'unit' => 'px',
					'size' => 30,
				],
				'selectors'  => [
					'{{WRAPPER}} .about-services-mixed-layout .layout-block' => 'margin-bottom: {{SIZE}}{{UNIT}};',
				],
			]
		);

		$this->add_control(
			'enable_hover_effects',
			[
				'label'        => esc_html__( 'Enable Hover Effects', 'bdevs-elementor' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => esc_html__( 'Yes', 'bdevs-elementor' ),
				'label_off'    => esc_html__( 'No', 'bdevs-elementor' ),
				'return_value' => 'yes',
				'default'      => 'yes',
				'description'  => esc_html__( 'Enable hover animations and effects for all blocks', 'bdevs-elementor' ),
			]
		);

		$this->add_control(
			'row_gap',
			[
				'label'      => esc_html__( 'Row Gap', 'bdevs-elementor' ),
				'type'       => Controls_Manager::SLIDER,
				'size_units' => [ 'px', 'em', 'rem' ],
				'range'      => [
					'px' => [
						'min' => 0,
						'max' => 100,
					],
				],
				'default'    => [
					'unit' => 'px',
					'size' => 60,
				],
				'selectors'  => [
					'{{WRAPPER}} .about-services-mixed-layout .layout-row' => 'margin-bottom: {{SIZE}}{{UNIT}};',
				],
				'description' => esc_html__( 'Space between rows of blocks', 'bdevs-elementor' ),
			]
		);

		$this->end_controls_section();
	}

	/**
	 * Get Bootstrap column class for a block
	 *
	 * @param array $block Block settings
	 * @return string Bootstrap column class
	 */
	private function get_block_column_class( $block ) {
		$desktop = 'col-lg-' . $block['column_width'];
		$tablet = 'col-md-' . $block['column_width_tablet'];
		$mobile = 'col-' . $block['column_width_mobile'];

		return $desktop . ' ' . $tablet . ' ' . $mobile;
	}

	/**
	 * Organize blocks into rows based on column widths (Legacy method for backward compatibility)
	 *
	 * @param array $blocks Array of block settings
	 * @return array Array of rows, each containing blocks
	 */
	private function organize_blocks_into_rows( $blocks ) {
		$rows = [];
		$current_row = [];
		$current_row_width = 0;

		foreach ( $blocks as $block ) {
			$block_width = intval( $block['column_width'] );

			// Check legacy force row break or new desktop force row break
			$force_break = false;
			if ( isset($block['force_row_break_desktop']) && $block['force_row_break_desktop'] === 'yes' ) {
				$force_break = true;
			} elseif ( isset($block['force_row_break']) && $block['force_row_break'] === 'yes' ) {
				$force_break = true; // Backward compatibility
			}

			if ( $force_break && !empty($current_row) ) {
				$rows[] = $current_row;
				$current_row = [];
				$current_row_width = 0;
			}

			// Check if adding this block would exceed 12 columns
			if ( $current_row_width + $block_width > 12 && !empty($current_row) ) {
				$rows[] = $current_row;
				$current_row = [];
				$current_row_width = 0;
			}

			// Add block to current row
			$current_row[] = $block;
			$current_row_width += $block_width;

			// If we've reached exactly 12 columns, start a new row
			if ( $current_row_width >= 12 ) {
				$rows[] = $current_row;
				$current_row = [];
				$current_row_width = 0;
			}
		}

		// Add any remaining blocks in the last row
		if ( !empty($current_row) ) {
			$rows[] = $current_row;
		}

		return $rows;
	}

	/**
	 * Organize blocks into responsive rows for all breakpoints
	 *
	 * @param array $blocks Array of block settings
	 * @return array Array with desktop, tablet, and mobile row structures
	 */
	private function organize_blocks_into_rows_responsive( $blocks ) {
		return [
			'desktop' => $this->organize_blocks_for_breakpoint( $blocks, 'desktop' ),
			'tablet'  => $this->organize_blocks_for_breakpoint( $blocks, 'tablet' ),
			'mobile'  => $this->organize_blocks_for_breakpoint( $blocks, 'mobile' )
		];
	}

	/**
	 * Organize blocks into rows for a specific breakpoint
	 *
	 * @param array $blocks Array of block settings
	 * @param string $breakpoint Breakpoint name (desktop, tablet, mobile)
	 * @return array Array of rows for the specified breakpoint
	 */
	private function organize_blocks_for_breakpoint( $blocks, $breakpoint ) {
		$rows = [];
		$current_row = [];
		$current_row_width = 0;

		// Determine column width and force break keys based on breakpoint
		$width_key = $breakpoint === 'desktop' ? 'column_width' : 'column_width_' . $breakpoint;
		$force_break_key = 'force_row_break_' . $breakpoint;

		foreach ( $blocks as $block ) {
			$block_width = intval( $block[$width_key] ?? $block['column_width'] );

			// Check breakpoint-specific force row break
			$force_break = false;
			if ( isset($block[$force_break_key]) && $block[$force_break_key] === 'yes' ) {
				$force_break = true;
			} elseif ( $breakpoint === 'desktop' && isset($block['force_row_break']) && $block['force_row_break'] === 'yes' ) {
				$force_break = true; // Backward compatibility for desktop
			}

			if ( $force_break && !empty($current_row) ) {
				$rows[] = $current_row;
				$current_row = [];
				$current_row_width = 0;
			}

			// Check if adding this block would exceed 12 columns
			if ( $current_row_width + $block_width > 12 && !empty($current_row) ) {
				$rows[] = $current_row;
				$current_row = [];
				$current_row_width = 0;
			}

			// Add block to current row
			$current_row[] = $block;
			$current_row_width += $block_width;

			// If we've reached exactly 12 columns, start a new row
			if ( $current_row_width >= 12 ) {
				$rows[] = $current_row;
				$current_row = [];
				$current_row_width = 0;
			}
		}

		// Add any remaining blocks in the last row
		if ( !empty($current_row) ) {
			$rows[] = $current_row;
		}

		return $rows;
	}

	/**
	 * Render widget output on the frontend.
	 *
	 * @since 1.0.0
	 * @access public
	 */
	public function render() {
		$settings = $this->get_settings_for_display();
		$hover_class = $settings['enable_hover_effects'] === 'yes' ? ' hover-enabled' : '';

		// Get HTML tags for section header with fallback to defaults
		$section_subheading_tag = !empty($settings['section_subheading_tag']) ? $settings['section_subheading_tag'] : 'h6';
		$section_heading_tag = !empty($settings['section_heading_tag']) ? $settings['section_heading_tag'] : 'h1';
		$section_description_tag = !empty($settings['section_description_tag']) ? $settings['section_description_tag'] : 'p';
		?>
		<section class="about-services-mixed-layout section-padding<?php echo esc_attr($hover_class); ?>">
			<div class="container">
				<!-- Section Header -->
				<?php if ( !empty($settings['section_subheading']) || !empty($settings['section_heading']) || !empty($settings['section_description']) ) : ?>
				<div class="row">
					<div class="col-12">
						<div class="section-header text-center mb-60">
							<?php if ( !empty($settings['section_subheading']) ) : ?>
							<<?php echo esc_attr($section_subheading_tag); ?> class="section-header-subheading section-subheading"><?php echo wp_kses_post($settings['section_subheading']); ?></<?php echo esc_attr($section_subheading_tag); ?>>
							<?php endif; ?>
							<?php if ( !empty($settings['section_heading']) ) : ?>
							<<?php echo esc_attr($section_heading_tag); ?> class="section-header-heading section-heading"><?php echo wp_kses_post($settings['section_heading']); ?></<?php echo esc_attr($section_heading_tag); ?>>
							<?php endif; ?>
							<?php if ( !empty($settings['section_description']) ) : ?>
							<<?php echo esc_attr($section_description_tag); ?> class="section-header-description section-description"><?php echo wp_kses_post($settings['section_description']); ?></<?php echo esc_attr($section_description_tag); ?>>
							<?php endif; ?>
						</div>
					</div>
				</div>
				<?php endif; ?>

				<!-- Flexible Layout Blocks -->
				<?php if ( !empty($settings['layout_blocks']) ) : ?>
				<div class="layout-blocks-section">
					<?php
					// Generate responsive row structures
					$responsive_rows = $this->organize_blocks_into_rows_responsive($settings['layout_blocks']);

					// Render blocks for each breakpoint
					foreach (['desktop', 'tablet', 'mobile'] as $breakpoint) :
						$rows = $responsive_rows[$breakpoint];
					?>
					<div class="layout-blocks-<?php echo esc_attr($breakpoint); ?>">
						<?php foreach ( $rows as $row_index => $row_blocks ) : ?>
						<div class="layout-row layout-row-<?php echo esc_attr($breakpoint); ?> row<?php echo $row_index > 0 ? ' mt-4' : ''; ?>">
							<?php foreach ( $row_blocks as $block ) : ?>
							<div class="<?php echo esc_attr($this->get_block_column_class($block)); ?>">
								<div class="layout-block layout-block-<?php echo esc_attr($block['block_type']); ?>">

								<?php if ( $block['block_type'] === 'image' ) :
									// Get HTML tags for image block with fallback to defaults
									$image_title_tag = !empty($block['image_title_tag']) ? $block['image_title_tag'] : 'h4';
									$image_subtitle_tag = !empty($block['image_subtitle_tag']) ? $block['image_subtitle_tag'] : 'h6';
								?>
								<!-- Image Block -->
								<div class="image-block">
									<div class="image-block-wrap">
										<?php if ( !empty($block['image_block_image']['url']) ) : ?>
										<div class="image-block-img">
											<?php
											$image_html = Group_Control_Image_Size::get_attachment_image_html( $block, 'image_block_size', 'image_block_image' );
											echo $image_html;
											?>
										</div>
										<?php endif; ?>

										<?php if ( !empty($block['image_title']) || !empty($block['image_subtitle']) ) : ?>
										<div class="image-block-content">
											<?php if ( !empty($block['image_title']) ) : ?>
											<<?php echo esc_attr($image_title_tag); ?> class="about-item-title image-block-title"><?php echo wp_kses_post($block['image_title']); ?></<?php echo esc_attr($image_title_tag); ?>>
											<?php endif; ?>
											<?php if ( !empty($block['image_subtitle']) ) : ?>
											<<?php echo esc_attr($image_subtitle_tag); ?> class="about-item-subtitle image-block-subtitle"><?php echo wp_kses_post($block['image_subtitle']); ?></<?php echo esc_attr($image_subtitle_tag); ?>>
											<?php endif; ?>
										</div>
										<?php endif; ?>

										<?php if ( !empty($block['image_link']['url']) ) : ?>
										<a href="<?php echo esc_url($block['image_link']['url']); ?>"
										   class="image-block-link"
										   <?php echo !empty($block['image_link']['is_external']) ? 'target="_blank"' : ''; ?>
										   <?php echo !empty($block['image_link']['nofollow']) ? 'rel="nofollow"' : ''; ?>
										   aria-label="<?php echo esc_attr($block['image_title']); ?>">
										</a>
										<?php endif; ?>
									</div>
								</div>

								<?php else :
									// Get HTML tags for content block with fallback to defaults
									$content_heading_tag = !empty($block['content_heading_tag']) ? $block['content_heading_tag'] : 'h4';
									$content_text_tag = !empty($block['content_text_tag']) ? $block['content_text_tag'] : 'p';
								?>
								<!-- Content Block -->
								<div class="content-block">
									<div class="content-block-wrap">
										<div class="content-block-inner">
											<?php if ( !empty($block['content_heading']) ) : ?>
												<?php if ( !empty($block['content_link']['url']) ) : ?>
													<a href="<?php echo esc_url($block['content_link']['url']); ?>"
													   class="content-block-link"
													   <?php echo !empty($block['content_link']['is_external']) ? 'target="_blank"' : ''; ?>
													   <?php echo !empty($block['content_link']['nofollow']) ? 'rel="nofollow"' : ''; ?>>
														<<?php echo esc_attr($content_heading_tag); ?> class="about-content-heading content-block-heading"><?php echo wp_kses_post($block['content_heading']); ?></<?php echo esc_attr($content_heading_tag); ?>>
													</a>
												<?php else : ?>
													<<?php echo esc_attr($content_heading_tag); ?> class="about-content-heading content-block-heading"><?php echo wp_kses_post($block['content_heading']); ?></<?php echo esc_attr($content_heading_tag); ?>>
												<?php endif; ?>
											<?php endif; ?>

											<?php if ( !empty($block['content_text']) ) : ?>
											<<?php echo esc_attr($content_text_tag); ?> class="section-header-description content-block-text"><?php echo wp_kses_post($block['content_text']); ?></<?php echo esc_attr($content_text_tag); ?>>
											<?php endif; ?>

											<?php if ( !empty($block['content_link']['url']) ) : ?>
											<div class="content-block-action">
												<a href="<?php echo esc_url($block['content_link']['url']); ?>"
												   class="content-block-arrow"
												   <?php echo !empty($block['content_link']['is_external']) ? 'target="_blank"' : ''; ?>
												   <?php echo !empty($block['content_link']['nofollow']) ? 'rel="nofollow"' : ''; ?>
												   aria-label="<?php echo esc_attr($block['content_heading']); ?>">
													<span class="fa-sharp fa-light fa-arrow-right" aria-hidden="true"></span>
												</a>
											</div>
											<?php endif; ?>
										</div>
									</div>
								</div>
								<?php endif; ?>

							</div>
						</div>
						<?php endforeach; ?>
					</div>
					<?php endforeach; ?>
				</div>
				<?php endforeach; ?>
			</div>
			<?php endif; ?>
		</section>
		<?php
	}
}
