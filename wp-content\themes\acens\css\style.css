/* ------------------------------------------------------------------

01. Typography style
02. Owl-Theme custom style
03. Preloader style
04. Navbar style
05. Image Animation style
06. For Images Reveal Effect style
07. Text Animation style
08. For Images Imago Effect style
09. Header style
10. Slider style
11. Parallax Header style
12. Kenburns SlideShow style
13. Video Background style
14. Section style
15. Banner Header style
16. About style
17. Pricing style
18. Team style
19. Team-Single style
20. Skills style
21. Works style
22. Work Single style
23. Services style
24. Services Sidebar style
25. Services 2 style
26. Gallery style
27. YouTube PopUp style
28. Accordion Box (for Faqs) style
29. LetsTalk style
30. Testimonials style
31. Clients style
32. Events style
33. Blog Home style
34. Blog 2 Grid style
35. Blog & Post style
36. Contact style
37. Button style
38. 404 style
39. Footer style
40. toTop Button style
41. Media Query style
42. Overlay Effect Bg image

------------------------------------------------------------------- */

/* ======= Typography style ======= */
:root {
  --clr-theme-color: #ab7b2a;
}

html,
body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  -moz-font-smoothing: antialiased;
  font-smoothing: antialiased;
}

body {
  font-family: "Hind", sans-serif;
  color: #727272;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  line-height: 1.75em;
  font-weight: 400;
}

img {
  width: 100%;
  height: auto;
}

/* typography */
/* headings */
h1 {
  font-size: clamp(
    3rem,
    calc(2.7rem + 0.6vw),
    3.25rem
  ); /* Responsive equivalent of 48px */
}

h2 {
  font-size: clamp(
    2.25rem,
    calc(2rem + 0.5vw),
    2.5rem
  ); /* Responsive equivalent of 36px */
}

h3 {
  font-size: clamp(
    1.688rem,
    calc(1.45rem + 0.4vw),
    1.813rem
  ); /* Responsive equivalent of 27px */
}

h4 {
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
}

h5 {
  font-size: clamp(
    1.25rem,
    calc(1.1rem + 0.35vw),
    1.375rem
  ); /* Responsive equivalent of 20px */
}

h6 {
  font-size: clamp(0.875rem, 1.6vw, 1rem); /* ~14px to 16px */
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  line-height: 1.25em;
  margin-bottom: 15px;
}

/* paragraph */
p {
  font-family: "Hind", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  line-height: 1.75em;
  margin: 0 0 15px;
  color: #727272;
  font-weight: 400;
}

/* lists */
ul {
  list-style-type: none;
}

/* links */
a {
  color: #2c3b4c;
  text-decoration: none;
}

a:hover {
  text-decoration: none;
  color: #2c3b4c;
}

a:link {
  text-decoration: none;
}

a:focus {
  outline: none;
}

img {
  width: 100%;
  height: auto;
  width: 100%;
  height: auto;
  -webkit-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}

/* form element */
button,
input,
optgroup,
select,
textarea {
  font-family: "Hind", sans-serif;
}

input[type="password"]:focus,
input[type="email"]:focus,
input[type="text"]:focus,
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus,
textarea:focus {
  outline: none;
}

input[type="password"],
input[type="email"],
input[type="text"],
input[type="file"],
textarea {
  max-width: 100%;
  margin-bottom: 15px;
  padding: 15px 20px;
  height: auto;
  background-color: #fff;
  -webkit-box-shadow: none;
  box-shadow: none;
  display: block;
  width: 100%;
  line-height: 1.5em;
  font-family: "Hind", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 400;
  color: #2c3b4c;
  background-image: none;
  border: 2px solid #f0f0f3;
  border-radius: 30px;
}

input:focus,
textarea:focus {
  border: 2px solid var(--clr-theme-color);
}

/* submit and alert success  */
input[type="submit"],
input[type="reset"],
input[type="button"],
button {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  line-height: 1.75em;
  -webkit-transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  border: none;
  border-radius: 80px;
  background-color: var(--clr-theme-color);
  color: #fff;
  line-height: 20px;
  text-align: center;
  padding: 20px 50px;
  height: auto;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

input[type="submit"]:hover,
input[type="reset"]:hover,
input[type="button"]:hover,
button:hover {
  border: none;
  background: #ab7b2a;
  color: #fff;
}

button.mfp-close:hover,
button.mfp-arrow:hover {
  background: transparent;
}

.alert-success {
  background: transparent;
  color: #727272;
  border: 2px solid var(--clr-theme-color);
  border-radius: 0px;
}

select {
  padding: 10px;
}

th,
tr,
td {
  padding: 10px 0;
}

input[type="radio"],
input[type="checkbox"] {
  display: inline;
}

/* placeholder */
::-webkit-input-placeholder {
  color: #727272;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 400;
}

:-moz-placeholder {
  color: #727272;
}

::-moz-placeholder {
  color: #727272;
  opacity: 1;
}

:-ms-input-placeholder {
  color: #727272;
}

/* important css */
.valign {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.v-middle {
  position: absolute;
  width: 100%;
  top: 50%;
  left: 0;
  -webkit-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  z-index: 9;
}

:root {
  scroll-behavior: auto;
}

.pb-0 {
  padding-bottom: 0px !important;
}

.pt-0 {
  padding-top: 0px !important;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-25 {
  margin-top: 25px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-45 {
  margin-top: 45px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-90 {
  margin-top: 90px;
}

.mt-120 {
  margin-top: 120px;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-5 {
  margin-bottom: 5px !important;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-90 {
  margin-bottom: 90px;
}

.mr-0 {
  margin-right: 0px !important;
}

.mr-15 {
  margin-right: 15px !important;
}

.mr-30 {
  margin-right: 30px !important;
}

.mr-40 {
  margin-right: 40px !important;
}

.mr-50 {
  margin-right: 50px !important;
}

.bg-fixed {
  background-attachment: fixed;
}

.bg-drk {
  background: #f0f0f3;
}

.bg-gry {
  background: #f0f0f3;
}

.bg-wht {
  background: #fff;
}

.cw {
  color: #fff;
}

.cd {
  color: #2c3b4c;
}

.bg-img {
  background-size: cover;
  background-repeat: no-repeat;
}

.bg-img-position-top {
  background-position: top;
}

.slider-fade .item.position-top,
.position-top {
  background-position: top;
}

.slider-fade .item.position-center,
.position-center {
  background-position: center;
}

.slider-fade .item.position-bottom,
.position-bottom {
  background-position: bottom;
}

.ontop {
  position: relative;
  z-index: 7;
}

.rest {
  padding: 0 !important;
  margin: 0 !important;
}

.d-flex {
  display: flex !important;
}

.full-width {
  width: 100% !important;
}

.position-relative {
  position: relative !important;
}

/* list style */
.ullist {
  display: grid;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
}

.ullist ul {
  position: relative;
  float: left;
  padding: 0px;
}

.ullist ul li {
  position: relative;
  line-height: 1.5em;
  margin-bottom: 10px;
  padding-left: 25px;
}

.ullist ul li:before {
  content: "\f00c";
  font-family: "Font Awesome 6 Pro";
  position: absolute;
  left: 0;
  top: 2px;
  color: var(--clr-theme-color);
  font-weight: 400;
  font-size: 12px;
}

/* list */
.list {
  position: relative;
  display: block;
}

.list li {
  position: relative;
  display: flex;
  align-items: baseline;
}

.list li + li {
  margin-top: 10px;
}

.list-icon {
  align-items: center;
  line-height: 0;
}

.list-icon i {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: var(--clr-theme-color);
}

.list-text {
  margin-left: 10px;
}

.list-text p {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  margin: 0;
  color: #727272;
}

.list-text p b {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  color: #2c3b4c;
}

/* vertical line */
.line-vr-section {
  position: relative;
  margin: -25px auto;
  border: 0;
  border-left: 3px solid;
  border-color: var(--clr-theme-color);
  width: 3px;
  height: 50px;
  z-index: 10;
  opacity: 1;
}

/* horizantal line */
.line-hr-section {
  width: 70px;
  border-top: 3px solid var(--clr-theme-color) !important;
  opacity: 1;
  margin: 15px 0;
}

/* horizantal line center */
.line-hr-section.center {
  margin: 15px auto;
}

/* image grayscale */
/* .img-grayscale img {
    -webkit-filter: grayscale(100%);
    -webkit-transition: .4s ease-in-out;
    -moz-filter: grayscale(100%);
    -moz-transition: .4s ease-in-out;
    -o-filter: grayscale(100%);
    -o-transition: .4s ease-in-out
}

.img-grayscale:hover img {
    -webkit-filter: grayscale(0%);
    -moz-filter: grayscale(0%);
    -o-filter: grayscale(0%)
} */

/* image normal (not grayscale) */
/* .img-grayscale img {
    -webkit-filter: grayscale(0%);
    -webkit-transition: .4s ease-in-out;
    -moz-filter: grayscale(0%);
    -moz-transition: .4s ease-in-out;
    -o-filter: grayscale(0%);
    -o-transition: .4s ease-in-out;
}

.img-grayscale:hover img {
    -webkit-filter: grayscale(0%);
    -moz-filter: grayscale(0%);
    -o-filter: grayscale(0%);
} */

/* image TEAM grayscale */
.img-team-grayscales img {
  -webkit-filter: grayscale(90%);
  -webkit-transition: 0.4s ease-in-out;
  -moz-filter: grayscale(90%);
  -moz-transition: 0.4s ease-in-out;
  -o-filter: grayscale(90%);
  -o-transition: 0.4s ease-in-out;
}

.img-team-grayscales:hover img {
  -webkit-filter: grayscale(0%);
  -moz-filter: grayscale(0%);
  -o-filter: grayscale(0%);
}

/* Normal image appearance */
.img-grayscale img {
  filter: saturate(130%) contrast(100%);
  transition: 0.4s ease-in-out;
}

/* More vivid color on hover */
.img-grayscale:hover img {
  filter: saturate(200%) contrast(120%);
}

/* blockquote */
blockquote {
  padding: 40px;
  display: block;
  position: relative;
  border: 2px solid var(--clr-theme-color);
  overflow: hidden;
  margin: 45px 0;
  color: #fff;
  background: var(--clr-theme-color);
  border-radius: 10px;
}

blockquote p {
  font-family: inherit;
  margin-bottom: 0 !important;
  color: inherit;
  max-width: 650px;
  width: 100%;
  position: relative;
  z-index: 3;
}

blockquote:before {
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  position: absolute;
  content: "\22";
  right: 40px;
  bottom: 40px;
  font-size: clamp(
    4.5rem,
    calc(4rem + 1.8vw),
    4.6875rem
  ); /* Responsive equivalent of 75px */
  opacity: 1;
  line-height: 1;
  color: #fff;
}

blockquote p {
  margin-bottom: 0;
}

blockquote p a {
  color: inherit;
}

blockquote cite {
  display: inline-block;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  position: relative;
  border-color: inherit;
  line-height: 1;
  margin-top: 22px;
  font-family: "Poppins", sans-serif;
  font-style: normal;
  color: #fff;
  font-weight: 900;
}

/* ul list */
ul.disc {
  list-style: none;
  margin-bottom: 30px;
  padding-left: 46px;
}

ul.disc li {
  margin-bottom: 10px;
  text-align: left;
}

ul.disc.disc li:before {
  content: "•";
  font-size: clamp(
    1.75rem,
    calc(1.5rem + 0.5vw),
    1.875rem
  ); /* Responsive equivalent of 30px */
  margin: 2px 0 0 -20px;
  color: var(--clr-theme-color);
  position: absolute;
}

/* ======= Owl-Theme custom style ======= */
/* owl dots */
.owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 10px;
  line-height: 1.5;
  display: block;
  outline: none;
}

.owl-theme .owl-dots {
  margin-top: 15px;
}

.owl-theme .owl-dots .owl-dot span {
  width: 14px;
  height: 14px;
  margin: 0 2px;
  border-radius: 50%;
  background: #ccc;
  border: 2px solid #ccc;
}

.slider-fade .owl-theme .owl-dots .owl-dot span {
  background: #fff;
  border: none;
}

.slider-fade .owl-theme .owl-dots .owl-dot.active span,
.slider-fade .owl-theme .owl-dots .owl-dot:hover span {
  background: var(--clr-theme-color);
  border: none;
}

.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
  background: var(--clr-theme-color);
  border: 1.5px solid var(--clr-theme-color);
}

.owl-theme .owl-dots button {
  background: transparent;
  padding: 2px;
}

/* owl-nav */
.owl-theme .owl-nav {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

.owl-theme .owl-prev {
  float: left;
  left: 0px !important;
}

.owl-theme .owl-next {
  float: right;
  right: 0px !important;
}

.owl-theme .owl-next > span {
  position: absolute;
  line-height: 0;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.owl-theme .owl-nav [class*="owl-"] {
  width: 55px;
  height: 55px;
  line-height: 45px;
  background: transparent;
  border-radius: 100%;
  color: #fff;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  margin-right: 30px;
  margin-left: 30px;
  cursor: pointer;
  border: 2px solid var(--clr-theme-color);
  transition: all 0.2s ease-in-out;
  transform: scale(1);
  opacity: 0;
}

.owl-theme .owl-nav [class*="owl-"]:hover {
  background: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}

.slider-fade .owl-theme:hover .owl-nav [class*="owl-"] {
  opacity: 1;
}

@media screen and (max-width: 768px) {
  .owl-theme .owl-nav {
    display: none;
  }
}

/* Equal height testimonial cards in Owl Carousel - only for testimonials section */
.testimonials .owl-stage {
  display: flex;
}
.testimonials .owl-item {
  display: flex;
  align-items: stretch;
}
.testimonials .item {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  height: 100%;
}

/* ======= Preloader style ======= */
.preloader-bg,
#preloader {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #fff;
  z-index: 10000009;
}

#preloader {
  display: table;
  table-layout: fixed;
}

#preloader-status {
  display: table-cell;
  vertical-align: middle;
}

.preloader-position {
  position: relative;
  margin: 0 auto;
  text-align: center;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}

.loader {
  position: relative;
  width: 70px;
  height: 70px;
  left: 50%;
  top: auto;
  margin-left: -22px;
  margin-top: 2px;
  -webkit-animation: rotate 1s infinite linear;
  -moz-animation: rotate 1s infinite linear;
  -ms-animation: rotate 1s infinite linear;
  -o-animation: rotate 1s infinite linear;
  animation: rotate 1s infinite linear;
  border: 2px solid rgba(0, 0, 0, 0.1);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
}

.loader span {
  position: absolute;
  width: 70px;
  height: 70px;
  top: -2px;
  left: -2px;
  border: 2px solid transparent;
  border-top: 2px solid var(--clr-theme-color);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
}

@-webkit-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* ======= Navbar Styles ======= */

/* Base Navbar */
.navbar {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  background: transparent;
  z-index: 99;
  padding: 0;
  height: 100px;
  border: none;
}

.navbar .container,
.nav-scroll .container {
  border: none;
}

/* Navbar Toggle - Animated Font Awesome Hamburger to X */
.navbar-toggler {
  position: relative;
  width: 30px;
  height: 30px;
  border: none;
  background: transparent;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-toggler-icon {
  background-image: none;
  position: relative;
  width: auto;
  height: auto;
  border: none;
}

.navbar-toggler-icon i {
  color: #fff;
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
  transition: all 0.3s ease-in-out;
  display: block;
}

/* Change icon when toggler is active */
.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon i::before {
  content: "\f00d"; /* Font Awesome X icon */
}

.navbar-toggler.collapsed .navbar-toggler-icon i::before {
  content: "\f0c9"; /* Font Awesome bars icon */
}

/* Color variations for scroll state */
.nav-scroll .navbar-toggler-icon i {
  color: #2c3b4c;
}

/* Smooth rotation animation */
.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon i {
  transform: rotate(90deg);
  color: #ab7b2a; /*X icon */
}

.navbar-toggler.collapsed .navbar-toggler-icon i {
  transform: rotate(0deg);
}

.navbar .icon-bar {
  color: #fff;
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
  width: 1.5em;
  height: 1em;
}

/* Navigation Links */
.navbar .navbar-nav .nav-link {
  font-family: "Poppins", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 900;
  color: #fff;
  margin: 1px;
  padding: 0 10px;
  transition: all 0.4s;
}

.navbar .navbar-nav .nav-link.nav-color {
  color: #2c3b4c;
}

.navbar .navbar-nav .nav-link:hover,
.navbar .navbar-nav .nav-link:hover i {
  color: var(--clr-theme-color);
}

.navbar .navbar-nav .active,
.navbar .navbar-nav .active i {
  color: var(--clr-theme-color) !important;
}

/* Scroll Navigation */
.nav-scroll {
  background: #fff;
  padding: 0;
  position: fixed;
  top: -100px;
  left: 0;
  width: 100%;
  border: none;
  box-shadow: 0px 5px 15px rgb(15 36 84 / 5%);
  transition: transform 0.5s;
  transform: translateY(100px);
}

.nav-scroll .navbar-toggler-icon,
.nav-scroll .icon-bar {
  color: #2c3b4c;
}

.nav-scroll .navbar-nav .nav-link {
  color: #2c3b4c;
}

.nav-scroll .navbar-nav .active,
.nav-scroll .navbar-nav .active i {
  color: var(--clr-theme-color);
}

/* Logo Styles */
.logo-wrapper {
  float: left;
}

.logo {
  padding: 0;
}

.logo-wrapper .logo h2,
.nav-scroll .logo-wrapper .logo h2 {
  font-family: "Poppins", sans-serif;
  font-size: clamp(
    2rem,
    calc(1.9rem + 0.2vw),
    2.5rem
  ); /* Responsive equivalent of 36px */
  font-weight: 900;
  display: block;
  width: 100%;
  position: relative;
  color: #fff;
  margin-bottom: 0;
  padding: 0;
  line-height: 1em;
}

.logo-wrapper .logo h2 span,
.logo-wrapper .logo h2 i,
.nav-scroll .logo-wrapper .logo h2 i {
  position: relative;
  color: var(--clr-theme-color);
}

.nav-scroll .navbar-nav .logo {
  padding: 15px 0;
  color: #fff;
}

/* ============================
   Default Logo Styles
   ============================ */
.logo-img {
  width: 70px;
  text-align: center;
  margin-bottom: 0;
}

.nav-scroll .logo-img {
  width: 60px;
}

/* ============================
     Tablet View (Max Width: 1024px)
     ============================ */
@media (max-width: 1024px) {
  .logo-img {
    width: 60px;
  }

  .nav-scroll .logo-img {
    width: 55px;
  }
}

/* ============================
     Mobile View (Max Width: 768px)
     ============================ */
@media (max-width: 768px) {
  .logo-img {
    width: 55px;
  }

  .nav-scroll .logo-img {
    width: 55px;
  }
}

/* Dropdown Styles */
.dropdown .nav-link i,
.nav-scroll .dropdown .nav-link i {
  padding-left: 1px;
  font-size: clamp(
    0.8rem,
    calc(0.85rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  color: var(--clr-theme-color);
}

.navbar .dropdown-toggle::after {
  display: none;
}

.navbar .dropdown-menu {
  box-shadow: 0 16px 50px rgb(27 27 27 / 7%);
}

.navbar .dropdown-menu li {
  border-bottom: none;
  position: relative;
  display: block;
  transition: all 500ms ease;
}

.navbar .dropdown-menu li:hover {
  padding-left: 5px;
}

.navbar .dropdown-menu .dropdown-item {
  padding: 0.4375rem 0;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: #2c3b4c;
  position: relative;
  background-color: transparent;
  transition: all 0.4s;
}

.navbar .dropdown-menu .dropdown-item span {
  display: block;
  cursor: pointer;
}

.navbar .dropdown-menu .dropdown-item i {
  padding: 10px 0 0 5px;
  font-size: clamp(
    0.8rem,
    calc(0.85rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  float: right;
  color: var(--clr-theme-color);
}

.navbar .dropdown-menu .dropdown:hover > .dropdown-item,
.navbar .dropdown-menu .dropdown-item.active,
.navbar .dropdown-menu .dropdown-item:hover {
  color: var(--clr-theme-color);
}

.navbar .dropdown-menu .dropdown-item.active span i {
  color: var(--clr-theme-color);
}

.navbar .dropdown-menu .dropdown-menu.pull-left {
  top: 0;
  left: auto;
  right: 100%;
}

/* Navbar Right Section */
.navbar .navbar-right {
  position: relative;
  display: flex;
  align-items: center;
}

.navbar .navbar-right .wrap {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 5;
  margin-left: 30px;
}

.navbar .navbar-right .wrap .icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: var(--clr-theme-color);
  transition: all 500ms ease;
}

.navbar .navbar-right .wrap .text {
  margin-left: 5px;
  top: 3px;
  position: relative;
}

.navbar .navbar-right .wrap .text p,
.nav-scroll .navbar-right .wrap .text p {
  font-family: "Architects Daughter", cursive;
  font-size: clamp(
    0.8rem,
    calc(0.85rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  line-height: 12px;
  color: var(--clr-theme-color);
  margin-bottom: 4px;
  font-weight: 400;
}

.navbar .navbar-right .wrap .text h5 {
  font-size: clamp(
    1.125rem,
    calc(1.05rem + 0.1vw),
    1.25rem
  ); /* Responsive equivalent of 18px */
  font-weight: 900;
  line-height: 25px;
  color: #fff;
  margin-bottom: 5px;
}

.navbar .navbar-right .wrap .text h5 a {
  color: #fff;
  transition: all 500ms ease;
}

.navbar .navbar-right .wrap .text h5 a:hover {
  color: #fff;
}

.nav-scroll .navbar-right .wrap .text h5,
.nav-scroll .navbar-right .wrap .text h5 a {
  color: #2c3b4c;
}

/* ======= Mobile Navbar Styles (991px and below) ======= */
@media screen and (max-width: 991px) {
  .navbar .navbar-right .wrap .text h5 a {
    color: #2c3b4c;
    transition: all 500ms ease;
  }

  .navbar .navbar-right .wrap .text h5 a:hover {
    color: #ab7b2a;
  }

  .navbar button:hover,
  .navbar button:active,
  .navbar button:focus {
    background: transparent;
    outline: none;
  }

  .navbar-toggler {
    padding: 0.5rem 0.75rem;
    border: none;
    background: transparent;
    z-index: 999;
  }

  .navbar-toggler:focus {
    outline: none;
    box-shadow: none;
  }

  .navbar-collapse {
    position: fixed;
    top: 100px;
    right: -100%;
    width: 100%;
    max-width: 100%;
    height: calc(100vh - 100px);
    padding: 15px 0;
    background: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2147483647 !important; /* max safe z-index */
    overflow-y: auto;
  }

  .navbar-collapse.show {
    right: 0;
  }

  .navbar-nav {
    padding: 10px 0;
  }

  .navbar .nav-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    margin: 0;
    padding: 10px 25px;
  }

  .navbar .nav-link {
    color: #2c3b4c !important;
    padding: 12px 0;
    font-size: clamp(
      0.9rem,
      calc(0.95rem + 0.05vw),
      1.1rem
    ); /* Responsive equivalent of 15px */
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .navbar .nav-link:hover,
  .navbar .nav-link:focus {
    color: var(--clr-theme-color) !important;
    background: transparent;
  }

  .navbar .dropdown-menu {
    background: rgba(0, 0, 0, 0.02);
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0;
    box-shadow: none;
    position: static;
    float: none;
    width: 100%;
    display: none;
  }

  .navbar .dropdown-menu.show {
    display: block;
    animation: fadeIn 0.3s ease;
  }

  .navbar .dropdown-item {
    color: #2c3b4c;
    padding: 12px 15px 12px 30px;
    font-size: clamp(
      0.9rem,
      calc(0.95rem + 0.05vw),
      1.1rem
    ); /* Responsive equivalent of 15px */
    font-weight: 500;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
  }

  .navbar .dropdown-item:hover,
  .navbar .dropdown-item:focus {
    color: var(--clr-theme-color);
    background: rgba(0, 0, 0, 0.03);
  }
}

/* ======= Image Animation style ======= */
.ripple-animation,
.morp-ani {
  --morp-value: 66% 24% 44% 56% / 44% 24% 70% 56%;
  --morp-md-value: 43% 38% 39% 35% / 44% 39% 43% 56%;
  --morp-time: 8s;
  --morp-spin-time: 20s;
  animation: morpspin var(--morp-spin-time) linear infinite reverse;
}

.morp-ani:before {
  -webkit-animation: vsmorph var(--morp-time) ease-in-out infinite both
    alternate;
  animation: vsmorph var(--morp-time) ease-in-out infinite both alternate;
}

.transform-banner img {
  -webkit-animation: border-transform 10s linear infinite alternate forwards;
  animation: border-transform 10s linear infinite alternate forwards;
  border-radius: 100%;
}

@-webkit-keyframes ripple {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0;
  }

  30% {
    opacity: 0.4;
  }

  100% {
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes ripple {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0;
  }

  30% {
    opacity: 0.4;
  }

  100% {
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
    opacity: 0;
  }
}

@-webkit-keyframes morph {
  0% {
    border-radius: 40% 60% 60% 40% / 60% 30% 70% 40%;
  }

  100% {
    border-radius: 40% 60%;
  }
}

@keyframes morph {
  0% {
    border-radius: 40% 60% 60% 40% / 60% 30% 70% 40%;
  }

  100% {
    border-radius: 40% 60%;
  }
}

@-webkit-keyframes vsmorph {
  0% {
    border-radius: var(--morp-value);
  }

  50% {
    border-radius: var(--morp-md-value);
  }

  100% {
    border-radius: 40% 60%;
  }
}

@keyframes vsmorph {
  0% {
    border-radius: var(--morp-value);
  }

  50% {
    border-radius: var(--morp-md-value);
  }

  100% {
    border-radius: 40% 60%;
  }
}

@-webkit-keyframes vsheromorph {
  0% {
    border-radius: 25% 80% 57% 47% / 53% 60% 43% 52%;
  }

  50% {
    border-radius: 40% 60% 60% 40% / 60% 30% 70% 40%;
  }

  100% {
    border-radius: 40% 60%;
  }
}

@keyframes vsheromorph {
  0% {
    border-radius: 25% 80% 57% 47% / 53% 60% 43% 52%;
  }

  50% {
    border-radius: 40% 60% 60% 40% / 60% 30% 70% 40%;
  }

  100% {
    border-radius: 40% 60%;
  }
}

@-webkit-keyframes morpspin {
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}

@keyframes morpspin {
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}

@-webkit-keyframes border-transform {
  0%,
  100% {
    border-radius: 63% 37% 54% 46%/55% 48% 52% 45%;
  }

  14% {
    border-radius: 40% 60% 54% 46%/49% 60% 40% 51%;
  }

  28% {
    border-radius: 54% 46% 38% 62%/49% 70% 30% 51%;
  }

  42% {
    border-radius: 61% 39% 55% 45%/61% 38% 62% 39%;
  }

  56% {
    border-radius: 61% 39% 67% 33%/70% 50% 50% 30%;
  }

  70% {
    border-radius: 50% 50% 34% 66%/56% 68% 32% 44%;
  }

  84% {
    border-radius: 46% 54% 50% 50%/35% 61% 39% 65%;
  }
}

@keyframes border-transform {
  0%,
  100% {
    border-radius: 63% 37% 54% 46%/55% 48% 52% 45%;
  }

  14% {
    border-radius: 40% 60% 54% 46%/49% 60% 40% 51%;
  }

  28% {
    border-radius: 54% 46% 38% 62%/49% 70% 30% 51%;
  }

  42% {
    border-radius: 61% 39% 55% 45%/61% 38% 62% 39%;
  }

  56% {
    border-radius: 61% 39% 67% 33%/70% 50% 50% 30%;
  }

  70% {
    border-radius: 50% 50% 34% 66%/56% 68% 32% 44%;
  }

  84% {
    border-radius: 46% 54% 50% 50%/35% 61% 39% 65%;
  }
}

@-webkit-keyframes moving {
  0% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }

  20% {
    -webkit-transform: translate(0px, -60px);
    transform: translate(0px, -60px);
  }

  50% {
    -webkit-transform: translate(-60px, -60px);
    transform: translate(-60px, -60px);
  }

  70% {
    -webkit-transform: translate(-60px, 0px);
    transform: translate(-60px, 0px);
  }

  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}

@keyframes moving {
  0% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }

  20% {
    -webkit-transform: translate(0px, -60px);
    transform: translate(0px, -60px);
  }

  50% {
    -webkit-transform: translate(-60px, -60px);
    transform: translate(-60px, -60px);
  }

  70% {
    -webkit-transform: translate(-60px, 0px);
    transform: translate(-60px, 0px);
  }

  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}

@-webkit-keyframes arrow-left {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  20% {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  40% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  60% {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes arrow-left {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  20% {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  40% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  60% {
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@-webkit-keyframes arrow-right {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  20% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }

  40% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  60% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes arrow-right {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  20% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }

  40% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  60% {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

/* ======= For Images Reveal Effect style ======= */
.reveal-effect {
  float: left;
  position: relative;
}

.reveal-effect.animated:before {
  content: "";
  width: 100%;
  height: 100%;
  background: var(--clr-theme-color);
  position: absolute;
  left: 0;
  top: 0;
  animation: 1s reveal linear forwards;
  -webkit-animation-duration: 1s;
  z-index: 1;
  -moz-animation-duration: 1s;
  -ms-animation-duration: 1s;
  -o-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  -ms-animation-fill-mode: forwards;
  -o-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -moz-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -o-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -ms-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
  animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.reveal-effect.animated > * {
  animation: 1s reveal-inner linear forwards;
}

@-webkit-keyframes reveal {
  0% {
    left: 0;
    width: 0;
  }

  50% {
    left: 0;
    width: 100%;
  }

  51% {
    left: auto;
    right: 0;
  }

  100% {
    left: auto;
    right: 0;
    width: 0;
  }
}

@-webkit-keyframes reveal-inner {
  0% {
    visibility: hidden;
    opacity: 0;
  }

  50% {
    visibility: hidden;
    opacity: 0;
  }

  51% {
    visibility: visible;
    opacity: 1;
  }

  100% {
    visibility: visible;
    opacity: 1;
  }
}

@media only screen and (max-width: 991px),
  only screen and (max-device-width: 991px) {
  .cursor {
    display: none;
  }
}

/* ======= Text Animation style ======= */
.splitting.animated .char {
  -webkit-animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  -webkit-animation-delay: calc(40ms * var(--char-index));
  animation-delay: calc(40ms * var(--char-index));
}

.splitting .whitespace {
  width: 10px;
}

@-webkit-keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

.splitting.txt.animated .char {
  -webkit-animation: fadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: fadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  -webkit-animation-delay: calc(20ms * var(--char-index));
  animation-delay: calc(20ms * var(--char-index));
}

.splitting.txt .whitespace {
  width: 5px;
}

@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

/* ======= Enhanced fadeIn Line Animation ======= */
/* Smooth per-line fade animation with optimized easing */
.fadeInLine {
  -webkit-animation: fadeInLineSmooth 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
  animation: fadeInLineSmooth 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@-webkit-keyframes fadeInLineSmooth {
  0% {
    opacity: 0;
    -webkit-transform: translateY(15px);
    transform: translateY(15px);
  }

  60% {
    opacity: 0.8;
    -webkit-transform: translateY(3px);
    transform: translateY(3px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInLineSmooth {
  0% {
    opacity: 0;
    -webkit-transform: translateY(15px);
    transform: translateY(15px);
  }

  60% {
    opacity: 0.8;
    -webkit-transform: translateY(3px);
    transform: translateY(3px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

/* Speed variations for fadeInLine */
.fadeInLine.animation-slow {
  -webkit-animation-duration: 1.8s;
  animation-duration: 1.8s;
}

.fadeInLine.animation-fast {
  -webkit-animation-duration: 0.8s;
  animation-duration: 0.8s;
}

.fadeInLine.animation-normal {
  -webkit-animation-duration: 1.2s;
  animation-duration: 1.2s;
}

/* ======= Enhanced Text Animation Styles ======= */
/* Word-level fade in animation for data-splitting */
.splitting[data-splitting-animation="fadeInWord"].animated .word {
  -webkit-animation: fadeInWord 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: fadeInWord 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  -webkit-animation-delay: calc(120ms * var(--word-index));
  animation-delay: calc(120ms * var(--word-index));
}

.splitting[data-splitting-animation="fadeInWord"] .word {
  display: inline-block;
  margin-right: 0.3em;
}

.splitting[data-splitting-animation="fadeInWord"] .word:last-child {
  margin-right: 0;
}

@-webkit-keyframes fadeInWord {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInWord {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

/* Zoom In Animation */
@-webkit-keyframes zoomIn {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes zoomIn {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

/* Slide In Up Animation */
@-webkit-keyframes slideInUp {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

/* Fade In Left Animation */
@-webkit-keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

/* Fade In Right Animation */
@-webkit-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

/* Slide In Down Animation */
@-webkit-keyframes slideInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

/* ======= Animation Speed Variations ======= */
/* Smooth easing for all animations */
.wow {
  -webkit-animation-timing-function: cubic-bezier(
    0.25,
    0.46,
    0.45,
    0.94
  ) !important;
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* Enhanced easing for entrance animations */
.wow.fadeIn,
.wow.fadeInLeft,
.wow.fadeInRight,
.wow.slideInUp,
.wow.slideInDown,
.wow.zoomIn {
  -webkit-animation-timing-function: cubic-bezier(
    0.175,
    0.885,
    0.32,
    1.275
  ) !important;
  animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
}

/* Gentle bounce for zoom animations */
.wow.zoomIn {
  -webkit-animation-timing-function: cubic-bezier(
    0.68,
    -0.55,
    0.265,
    1.55
  ) !important;
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
}

/* ======= For Images Imago Effect style ======= */
.imago {
  -webkit-clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
  clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
  -webkit-transition: all 0.8s;
  -o-transition: all 0.8s;
  transition: all 0.8s;
  -webkit-transition-delay: 0.3s;
  -o-transition-delay: 0.3s;
  transition-delay: 0.3s;
}

.imago.animated {
  -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}

/* ======= Header style ======= */
.header {
  height: 100vh;
  overflow: hidden;
}

.header .caption .o-hidden {
  display: inline-block;
}

.header .caption h1,
.header .caption[data-overlay-dark] h1 {
  position: relative;
  font-size: clamp(
    4rem,
    calc(4.5rem + 1vw),
    6.875rem
  ); /* Responsive equivalent of 110px */
  letter-spacing: 3px;
  line-height: 120px;
  color: #ba7b22;
  margin-bottom: 20px;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.header .caption h1 span,
.header .caption[data-overlay-dark] h1 span {
  font-size: clamp(
    6.25rem,
    calc(5.5rem + 0.5vw),
    6.5rem
  ); /* Responsive equivalent of 100px */
  color: transparent;
  -webkit-text-stroke: 1px #ba7b22;
  opacity: 0.8;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.header .caption p,
.header .caption[data-overlay-dark] p {
  font-family: "Hind", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: #fff;
  margin-bottom: 15px;
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}

.header .caption .btn-1,
.header .caption .btn-2 {
  -webkit-animation-delay: 0.7s;
  animation-delay: 0.7s;
}

/* ======= Slider style ======= */
.slider-fade .owl-item {
  height: 100vh;
  position: relative;
}

.slider-fade .item {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-size: cover;
  background-position: center;
}

.slider-fade .item .caption {
  z-index: 9;
}

/* Hide any cloned slides to prevent HTML duplication */
.slider-fade .owl-item.cloned {
  display: none !important;
}

/* owl-theme owl-dots */
.slider-fade .owl-theme .owl-dots {
  position: absolute;
  bottom: 6vh;
  width: 100%;
}

.slider-fade .owl-theme .owl-dots .owl-dot span {
  background: #fff;
  border: none;
}

.slider-fade .owl-theme .owl-dots .owl-dot.active span,
.slider-fade .owl-theme .owl-dots .owl-dot:hover span {
  background: var(--clr-theme-color);
  border: none;
}

/* owl-theme owl-nav */
.slider-fade .owl-theme .owl-nav {
  position: absolute !important;
  top: 45% !important;
  bottom: auto !important;
  width: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}

.slider-fade .owl-theme .owl-prev {
  left: 10px !important;
}

.slider-fade .owl-theme .owl-next {
  right: 10px !important;
}

.slider-fade .owl-theme .owl-prev > span,
.slider-fade .owl-theme .owl-next > span {
  position: absolute;
  line-height: 0;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.slider-fade .owl-theme .owl-nav [class*="owl-"] {
  width: 55px;
  height: 55px;
  line-height: 45px;
  background: transparent;
  border-radius: 100%;
  color: #fff;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  margin-right: 30px;
  margin-left: 30px;
  cursor: pointer;
  border: 2px solid var(--clr-theme-color);
  transition: all 0.2s ease-in-out;
  transform: scale(1);
  opacity: 0;
}

.slider-fade .owl-theme .owl-nav [class*="owl-"]:hover {
  background: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}

.slider-fade .owl-theme:hover .owl-nav [class*="owl-"] {
  opacity: 1;
}

@media screen and (max-width: 768px) {
  .slider-fade .owl-theme .owl-nav {
    display: none;
  }
}

/* ======= Parallax Header style ======= */
.parallax-header {
  height: 100vh;
  background-position: center;
  overflow: hidden;
}

.parallax-header.position-top {
  background-position: top;
}

.parallax-header.position-center {
  background-position: center;
}

.parallax-header.position-bottom {
  background-position: bottom;
}

.parallax-header h6,
.parallax-header[data-overlay-dark] h6 {
  display: inline-block;
  font-weight: 400;
  font-size: clamp(
    0.75rem,
    calc(0.8rem + 0.05vw),
    0.875rem
  ); /* Responsive equivalent of 12px */
  color: var(--clr-theme-color);
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.parallax-header h1,
.parallax-header[data-overlay-dark] h1 {
  position: relative;
  font-size: clamp(
    4rem,
    calc(4.5rem + 1vw),
    6.875rem
  ); /* Responsive equivalent of 110px */
  line-height: 120px;
  letter-spacing: 3px;
  font-weight: 900;
  color: #fff;
  margin-bottom: 15px;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

.parallax-header h1 span,
.parallax-header[data-overlay-dark] h1 span {
  position: relative;
  font-size: clamp(
    6.25rem,
    calc(5.5rem + 0.5vw),
    6.5rem
  ); /* Responsive equivalent of 100px */
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
  color: transparent;
  -webkit-text-stroke: 1px #fff;
  opacity: 0.8;
}

.parallax-header[data-overlay-dark] p {
  font-family: "Hind", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: #fff;
  margin-bottom: 15px;
}

/* ===== Kenburns SlideShow style ===== */
.kenburns-section {
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  position: relative;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  height: 100vh;
}

.kenburns-section::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -5;
  background: #2c3b4c;
}

.kenburns-inner {
  position: relative;
  z-index: 15;
}

.kenburns-inner .caption {
  position: relative;
}

.kenburns-inner .caption h6,
.kenburns-inner .caption[data-overlay-dark] h6 {
  display: inline-block;
  font-weight: 900;
  font-size: clamp(
    0.625rem,
    calc(0.55rem + 0.15vw),
    0.75rem
  ); /* Responsive equivalent of 12px */
  color: var(--clr-theme-color);
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.kenburns-inner .caption h1,
.kenburns-inner .caption[data-overlay-dark] h1 {
  position: relative;
  font-size: clamp(5.15625rem, 11vw, 6.875rem);
  line-height: 7.5rem;
  letter-spacing: 0.1875rem;
  font-weight: 900;
  color: #fff;
  margin-bottom: 15px;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

.kenburns-inner .caption h1 span,
.kenburns-inner .caption[data-overlay-dark] h1 span {
  position: relative;
  font-size: clamp(
    6rem,
    calc(6.2rem + 0.2vw),
    6.8rem
  ); /* Responsive equivalent of 100px */
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
  color: transparent;
  -webkit-text-stroke: 1px #fff;
  opacity: 0.8;
}

.kenburns-inner .caption p,
.kenburns-inner .caption[data-overlay-dark] p {
  font-family: "Hind", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: #fff;
  margin-bottom: 15px;
}

#kenburnsSliderContainer .vegas-overlay {
  outline: none;
}

@media screen and (max-width: 767px) {
  .kenburns-inner .caption h6 {
    font-size: clamp(
      0.75rem,
      calc(0.8rem + 0.05vw),
      0.875rem
    ); /* Responsive equivalent of 12px */
  }

  .kenburns-inner .caption h1 {
    font-size: clamp(
      3.625rem,
      calc(3.7rem + 0.05vw),
      3.875rem
    ); /* Responsive equivalent of 60px */
  }
}

/* =======  Video Background style  ======= */
.video-fullscreen {
  overflow: hidden;
}

.video-fullscreen .video.grayscale {
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}

.video-fullscreen .video {
  height: 100vh;
}

.video-fullscreen video {
  width: 100vw;
  min-width: 100%;
  height: 100vh;
  -o-object-fit: cover;
  object-fit: cover;
}

.video-fullscreen .v-middle {
  z-index: 1;
}

.video-fullscreen h6,
.video-fullscreen[data-overlay-dark] h6 {
  display: inline-block;
  font-weight: 400;
  font-size: clamp(
    0.75rem,
    calc(0.8rem + 0.05vw),
    0.875rem
  ); /* Responsive equivalent of 12px */
  color: var(--clr-theme-color);
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.video-fullscreen h1,
.video-fullscreen[data-overlay-dark] h1 {
  position: relative;
  font-size: clamp(
    4rem,
    calc(4.5rem + 1vw),
    6.875rem
  ); /* Responsive equivalent of 110px */
  line-height: 120px;
  letter-spacing: 3px;
  font-weight: 900;
  color: #fff;
  margin-bottom: 15px;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

.video-fullscreen h1 span,
.video-fullscreen[data-overlay-dark] h1 span {
  position: relative;
  font-size: clamp(
    6.25rem,
    calc(5.5rem + 0.5vw),
    6.5rem
  ); /* Responsive equivalent of 100px */
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
  color: transparent;
  -webkit-text-stroke: 1px #fff;
  opacity: 0.8;
}

.video-fullscreen p,
.video-fullscreen[data-overlay-dark] p {
  font-family: "Hind", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: #fff;
  margin-bottom: 15px;
}

/* ======= Section style ======= */
.section-padding {
  padding: clamp(40px, 10vw, 120px) 0;
}

.section-padding h1 {
  font-size: clamp(
    3rem,
    calc(2.7rem + 0.6vw),
    3.25rem
  ); /* Responsive equivalent of 48px */
  font-weight: 900;
  margin-bottom: 15px;
  color: #2c3b4c;
}

.section-padding h1.cd {
  color: #2c3b4c;
}

.section-padding h1.cw {
  color: #fff;
}

.section-padding h2 {
  font-size: clamp(
    3rem,
    calc(2.7rem + 0.6vw),
    3.25rem
  ); /* Responsive equivalent of 48px */
  line-height: 1.2em;
  font-weight: 900;
  margin-bottom: 5px;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
}

.section-padding h5 {
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
  line-height: 1.5em;
  font-weight: 900;
  margin-bottom: 15px;
}

.section-padding[data-overlay-dark] h6,
.section-padding h6 {
  display: inline-block;
  font-family: "Architects Daughter", cursive;
  font-size: clamp(
    1.125rem,
    calc(1.05rem + 0.1vw),
    1.25rem
  ); /* Responsive equivalent of 18px */
  font-weight: 400;
  color: var(--clr-theme-color);
  margin-bottom: 5px;
}

/* ======= Banner Header style ======= */
.banner-header {
  height: 70vh;
  background-position: center;
}

.banner-header.full-height {
  min-height: 100vh;
}

.banner-header[data-overlay-dark] h1,
.banner-header h1 {
  font-size: clamp(
    2.25rem,
    calc(1.978rem + 0.085vw),
    3rem
  ); /* Converted from 48px and 36px */
  color: #fff;
  position: relative;
  line-height: 1.25em;
  margin-bottom: 0;
}

.banner-header p {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  margin-bottom: 0;
  color: #fff;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}

/* breadcrumbs */
.banner-header .breadcrumbs {
  list-style: none;
  margin: 20px 0 0;
  padding: 0;
}

.banner-header .breadcrumbs li {
  display: inline-block;
  margin: 0;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 900;
  font-family: "Poppins", sans-serif;
  color: #fff;
}

.banner-header .breadcrumbs li a {
  color: var(--clr-theme-color);
}

.banner-header .breadcrumbs li a:hover {
  color: #fff;
}

.banner-header .breadcrumbs li:not(:last-child):after {
  margin: 0 20px 0 20px;
  vertical-align: middle;
  position: relative;
  top: 0px;
  display: inline-block;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "|";
  font-size: clamp(
    0.75rem,
    calc(0.8rem + 0.05vw),
    0.875rem
  ); /* Responsive equivalent of 12px */
  font-weight: 900;
  color: var(--clr-theme-color);
}

@media screen and (max-width: 767px) {
  .banner-header h1 {
    font-size: clamp(
      2.25rem,
      calc(1.978rem + 0.085vw),
      3rem
    ); /* Converted from 36px */
  }
}

/* ======= About style ======= */
.about {
  position: relative;
}

.about .item {
  position: relative;
  margin-bottom: 1.5rem;
}

.about .item .wrap {
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.about .item .wrap .img {
  position: relative;
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  overflow: hidden;
  border-radius: 10px;
  background: #101010;
}

.about .item .wrap .img img {
  width: 100%;
  margin: 0;
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  background-size: cover;
  opacity: 1;
}

.about .item .wrap:hover .img {
  transform: scale(0.98);
}

.about .item .wrap .img:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: rgb(16, 16, 16);
  background: linear-gradient(
    0deg,
    rgba(16, 16, 16, 0.9528186274509804) 20%,
    rgba(16, 16, 16, 0) 75%
  );
}

.about .item .wrap .title {
  position: absolute;
  bottom: 0px;
  padding: 30px 30px 30px 45px;
  transform: translateX(-20px);
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
}

.about .item .wrap .title h4 {
  color: #fff;
  font-size: clamp(
    1.25rem,
    calc(1.1rem + 0.2vw),
    1.5rem
  ); /* Converted from 24px */
  margin-bottom: 5px;
}

.about .item .wrap .title h6 {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-family: "Didact Gothic", sans-serif;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
  text-transform: none;
  letter-spacing: 0;
}

.about .item .wrap:hover .title {
  transform: translateY(-10px);
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
}

.about h5 {
  font-size: clamp(
    1.25rem,
    calc(1.1rem + 0.2vw),
    1.5rem
  ); /* Converted from 24px */
}

/* ======= About-Box style ======= */
.about-box {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 30px; /* optional spacing between items */
}

.about-box .item {
  background-color: #313b4b0a;
  position: relative;
  border-radius: 0;
  transition: 0.7s;
  border: none;
  border-radius: 7px;
  flex: 1 1 100%; /* Make it responsive */
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* If you're using a grid with columns, set a fixed basis */
@media (min-width: 768px) {
  .about-box .item {
    flex: 1 1 calc(33.333% - 30px); /* 3 columns minus the gap */
  }
}

.about-box .item .wrap {
  transition: 0.7s;
  padding: 75px 30px 15px 30px;
  flex-grow: 1; /* Ensures wrap grows to fill the height */
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Keeps h4 and p spaced nicely */
  height: 100%;
}

.about-box .item .wrap .con {
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.about-box .item .wrap .con h4 {
  font-size: clamp(
    1.25rem,
    calc(1.1rem + 0.2vw),
    1.5rem
  ); /* Converted from 24px */
  color: #2c3b4c;
}

.about-box .item .wrap .con p {
  color: #727272;
}

.about-box .item .wrap .con .icon-2 a {
  color: #f70000;
  font-size: clamp(
    1.25rem,
    calc(1.1rem + 0.2vw),
    1.5rem
  ); /* Converted from 24px */
  opacity: 0;
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
}

.about-box .item:hover {
  background-color: #2c3b4c;
}

.about-box .item:hover .wrap .con {
  color: #fff;
  transform: translateY(-25px);
}

.about-box .item:hover .wrap .con h4,
.about-box .item:hover .wrap .con p,
.about-box .item:hover .wrap .con .icon-2 a {
  color: #fff;
  opacity: 1;
}

/* ======= Pricing style ======= */
.price .item {
  padding: 60px 30px 40px;
  position: relative;
  background-color: #f0f0f3;
  overflow: hidden;
  border-radius: 10px;
}

.price .item .type {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 10px 20px;
  color: #fff;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  background: var(--clr-theme-color);
  border-radius: 30px;
  border: 2px solid var(--clr-theme-color);
}

.price .item .content h2 {
  font-size: clamp(
    2.5rem,
    calc(2.2rem + 0.4vw),
    3.75rem
  ); /* Converted from 60px */
  color: #2c3b4c;
  font-weight: 900;
  display: inline-flex;
}

.price .item .content h2 span {
  font-family: "Hind", sans-serif;
  color: var(--clr-theme-color);
  font-weight: 400;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  padding-left: 10px;
}

.price .item .content .feat li {
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  color: #727272;
}

.price .item .content .feat li i {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: var(--clr-theme-color);
  margin-right: 10px;
  padding-top: 10px;
}

.price .item .numb {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-size: clamp(
    3rem,
    calc(2.5rem + 0.8vw),
    5.25rem
  ); /* Converted from 84px */
  position: absolute;
  top: -10px;
  left: 40px;
  color: transparent;
  -webkit-text-stroke: 2px rgba(0, 0, 0, 0.05);
  opacity: 0.8;
  transform-origin: 0 0;
  transform: rotate(90deg);
  text-transform: uppercase;
}

@media screen and (max-width: 768px) {
  .price .item .content {
    display: block !important;
  }

  .price .item .content .mr-40 {
    margin-bottom: 40px !important;
  }
}

/* ======= Team style ======= */
.team {
  position: relative;
}

.team .item {
  position: relative;
}

.team .item .img {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  height: 300px; /* Make sure it takes up the full height of its container */
}

.team .item .img img {
  width: 100%;
  height: 300px; /* Ensure the image covers the container */
  object-fit: cover; /* Make sure the image fills the container and maintains aspect ratio */
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.team .item .img:hover img {
  -webkit-filter: none;
  filter: none;
  -webkit-transform: scale(1.09, 1.09);
  transform: scale(1.09, 1.09);
  -webkit-transition: all 1s ease;
  -moz-transition: all 1s ease;
  -o-transition: all 1s ease;
  -ms-transition: all 1s ease;
  transition: all 1s ease;
}

.team .item .con {
  position: relative;
  margin-left: 20px;
  margin-right: 20px;
  bottom: 45px;
  z-index: 2;
  background: #f0f0f3;
  padding: 25px 20px;
  opacity: 1;
  visibility: visible;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: auto;
  overflow: hidden;
  z-index: 1;
  border-radius: 10px;
}

.team .item:hover .con {
  background: #253b4d;
}

.team .item:hover .con h5 {
  transform: translateX(10px);
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
}

.team .item .con .arrow {
  position: relative;
  display: block;
  top: 0px;
  visibility: hidden;
  opacity: 0;
  transform: rotate(0);
}

.team .item:hover .con .arrow {
  visibility: visible;
  transform: translateY(0%);
  opacity: 1;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.team .item .con .arrow a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: var(--clr-theme-color);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  border-radius: 100%;
  background: #fff;
}

.team .item .con .arrow a:hover {
  background: #fff;
  color: var(--clr-theme-color);
}

.team .item .con h5 {
  font-size: clamp(
    1.125rem,
    calc(1rem + 0.3vw),
    1.25rem
  ); /* Responsive equivalent of 18px */
  color: #2c3b4c;
  line-height: 1.5em;
}

.team .item .con h5 a {
  color: #2c3b4c;
}

.team .item .con h5 span {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: #727272;
  text-transform: none;
  font-weight: 400;
  font-family: "Hind", sans-serif;
  letter-spacing: 0;
}

.team .item:hover .con h5,
.team .item:hover .con h5 a,
.team .item:hover .con h5 span {
  color: #fff;
}

/* ======= Team-Single style ======= */
.team-single img:hover {
  transform: scale(0.98);
}

.team-single .img {
  position: relative;
}

.team-single .img {
  padding: 0 30px 30px 15px;
  position: relative;
}

.team-single .img:before {
  content: "";
  position: absolute;
  top: 30px;
  right: 0;
  left: 45px;
  bottom: 0;
  background-color: var(--clr-theme-color);
  border-radius: 10px;
}

.team-single .img img {
  position: relative;
  z-index: 2;
  background: transparent;
  border-radius: 10px;
}

/* details */
.team-single .wrapper {
  position: relative;
}

.team-single .wrapper .cont {
  width: 100%;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  padding: 30px 0 0 0;
}

.team-single .wrapper .cont .coll {
  width: auto;
  max-width: auto;
}

.team-single .wrapper .cont .coll h6 {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 900;
  line-height: 1.2em;
  white-space: normal;
  margin: 0;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  letter-spacing: 0;
}

.team-single .wrapper .cont .coll h5 {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 900;
  white-space: normal;
  margin: 0;
  margin-top: 0;
  margin-bottom: 0;
  vertical-align: middle;
  transition: color 0.3s;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  letter-spacing: 0;
}

/* link */
.team-single p a {
  position: relative;
  color: #2c3b4c;
  text-decoration: none;
}

.team-single p a:before {
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--clr-theme-color);
  content: "";
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.team-single p a:hover {
  color: #2c3b4c;
}

/* tabs */
.team-single .con .simpl-bord.nav-tabs {
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 2px solid var(--clr-theme-color);
}

.team-single .con .simpl-bord .nav-item {
  margin-right: 30px;
}

.team-single .con .simpl-bord .nav-item:last-of-type {
  margin-right: 0;
}

.team-single .con .simpl-bord .nav-link {
  padding: 0;
  border: 0;
  color: inherit;
  background: transparent !important;
  font-weight: 900;
  cursor: pointer;
  color: #2c3b4c;
  font-size: clamp(
    1.125rem,
    calc(1rem + 0.3vw),
    1.25rem
  ); /* Responsive equivalent of 18px */
  font-family: "Poppins", sans-serif;
}

.team-single .con .simpl-bord .nav-link.active {
  font-weight: 900;
  color: var(--clr-theme-color);
  font-family: "Poppins", sans-serif;
  font-size: clamp(
    1.125rem,
    calc(1rem + 0.3vw),
    1.25rem
  ); /* Responsive equivalent of 18px */
}

.team-single .tab-content p {
  color: #727272;
}

/* team-single social */
.team-single .social-icon {
  margin-bottom: 0px;
}

.team-single .social-icon a {
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: clamp(
    1.125rem,
    calc(1rem + 0.3vw),
    1.25rem
  ); /* Responsive equivalent of 18px */
  border: 2px solid var(--clr-theme-color);
  background: var(--clr-theme-color);
  color: #fff;
  border-radius: 0;
  margin-right: 5px;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  border-radius: 100%;
  display: inline-block;
}

.team-single .social-icon a:hover {
  border: 2px solid #2c3b4c;
  background: #2c3b4c;
  color: #fff;
}

/* ======= Skills style ======= */
.skills {
  position: relative;
}

.skills .skill-item {
  margin-bottom: 40px;
}

.skills .skill-item h6 {
  font-family: "Hind", sans-serif;
  font-weight: 400;
  color: #2c3b4c;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  margin-bottom: 15px;
}

.skills-box .skill-progress {
  height: 4px;
  background-color: rgba(0, 0, 0, 0.07);
  position: relative;
}

.skills-box .skill-progress .progres {
  position: absolute;
  height: 100%;
  width: 0;
  top: 0;
  left: 0;
  background-color: var(--clr-theme-color);
  -webkit-transition: all 1.5s;
  -o-transition: all 1.5s;
  transition: all 1.5s;
}

.skills-box .skill-progress .progres:after {
  content: attr(data-value);
  position: absolute;
  right: 10px;
  top: -25px;
  font-family: "Poppins", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 900;
  color: #2c3b4c;
}

/* ======= Works Cards ======= */
.works {
  overflow: hidden;
}

/* Flex column layout */
.works .item {
  margin-bottom: 25px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.works .wrap {
  position: relative;
  height: 300px;
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  background-color: #f0f0f3;
  cursor: pointer;
}

/* Image container */
.works .wrap .img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.works .wrap .img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 10px;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

/* Overlay gradient on hover */
.works .wrap::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(32, 29, 29, 0));
  z-index: 2;
  opacity: 0;
  transition: opacity 0.5s ease;
  border-radius: 10px;
}

/* Show overlay on hover */
.works .wrap:hover::before {
  opacity: 1;
}

/* Animate image on hover */
.works .wrap:hover .img img {
  transform: scale(1.05);
  opacity: 0.85;
}

/* Text content */
.works .text {
  position: relative;
  z-index: 3;
  width: 100%;
  padding: 1.2rem;
  text-align: center;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

/* Show text on hover */
.works .wrap:hover .text {
  opacity: 1;
  transform: translateY(0);
}

/* Title */
.works .text h4 {
  font-size: clamp(1rem, 1.5vw + 0.4rem, 1.3rem); /* Responsive size */
  color: #fff;
  margin-bottom: 5px;
  text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.7),
    -1px -1px 1px rgba(255, 255, 255, 0.2);
  line-height: 1.3;
}

/* Category / subtitle */
.works .text p {
  font-family: "Architects Daughter", cursive;
  font-size: clamp(1.1rem, 1.1rem, 1.1rem); /* Converted from 1.1rem */
  color: var(--clr-theme-color, #ffd700);
  font-weight: 400;
  margin-bottom: 0;
}

/* icon box button */
.works .item .wrap .text .icon-box {
  position: relative;
  display: block;
}

.works .item .wrap .text a .icon-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 55px;
  height: 55px;
  border-radius: 50%;
  transition: all 200ms linear;
  transition-delay: 0.3s;
  z-index: 1;
  margin: 0 auto;
}

.works .item .wrap .text a .icon-box::before {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  border: 2px solid var(--clr-theme-color);
  background: var(--clr-theme-color);
  border-radius: 50%;
  transform: scale(0.7);
  opacity: 0;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
  z-index: 1;
  content: "";
}

.works .item:hover .wrap .text a .icon-box::before {
  transform: scale(1);
  opacity: 1;
}

.works .item .wrap .text a .icon-box i::before {
  position: relative;
  display: inline-block;
  color: #fff;
  font-size: clamp(
    1.25rem,
    calc(1.1rem + 0.2vw),
    1.5rem
  ); /* Converted from 24px */
  -webkit-transform: rotate(-40deg);
  transform: rotate(-40deg);
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.works .item:hover .wrap .text a .icon-box i::before {
  transform: rotate(0);
  color: #fff;
  z-index: 9;
}

/* btn-1 */
.works .item:hover .wrap .text .btn-1 i {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.works .item:hover .wrap .text.btn-1:hover i {
  transform: rotate(0);
}

/* ----------------------------- */

/* --- Modern Card Style for Projects Pages 2 Widget --- */
.projects2-card,
.projects2-card * {
  font-family: "Poppins", sans-serif;
}

/* Make all cards in a row equal height */
.row .col-lg-4:has(.projects2-card),
.row .col-md-6:has(.projects2-card),
.row .col-sm-12:has(.projects2-card) {
  display: flex;
}

.projects2-card {
  display: flex;
  flex-direction: column;
  flex: 1 1 100%;
  min-height: 100%;
  height: 100%;
  background: var(--clr-bg, #fff);
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 32px 0 rgba(30, 34, 90, 0.13);
  overflow: hidden;
  transition: box-shadow 0.3s cubic-bezier(0.4, 2, 0.6, 1),
    transform 0.3s cubic-bezier(0.4, 2, 0.6, 1);
}

.projects2-card:hover {
  box-shadow: 0 16px 40px 0 rgba(30, 34, 90, 0.22);
  transform: translateY(-10px) scale(1.025);
  border: solid 1px #ba7b24;
}

.projects2-card-img {
  display: block;
  width: 100%;
  height: auto;
  max-height: 200px;
  object-position: center;
  object-fit: cover;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  transition: transform 0.4s cubic-bezier(0.4, 2, 0.6, 1);
}

.projects2-card:hover .projects2-card-img {
  transform: scale(1.05);
}

.projects2-card-body {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  justify-content: space-between;
  padding: clamp(16px, 3vw, 32px) clamp(12px, 2vw, 28px) clamp(12px, 2vw, 22px)
    clamp(12px, 2vw, 28px);
  background: var(--clr-bg, #fff);
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}

/* Category styles */
.projects2-card-category,
.projects2-card-category-link {
  display: inline-block;
  width: auto;
  max-width: fit-content;
  flex: 0 0 auto;
  min-width: 0;
  padding: clamp(8px, 1vw, 10px) clamp(10px, 1.5vw, 12px);
  margin-right: clamp(4px, 1vw, 10px);
  background: #b8862b;
  color: #fff;
  font-size: clamp(11px, 1.1vw, 14px);
  font-weight: 500;
  line-height: 0;
  text-align: left;
  letter-spacing: 0.02em;
  white-space: nowrap;
  border: none;
  border-radius: 999px;
  box-shadow: 0 2px 8px 0 rgba(184, 134, 43, 0.1);
  text-decoration: none;
  transition: background 0.2s, color 0.2s, transform 0.2s, box-shadow 0.2s;
}

.projects2-card-category {
  margin-right: 0;
}

.projects2-card-category-link:hover,
.projects2-card-category-link:focus,
.projects2-card-category:hover,
.projects2-card-category:focus {
  background: #2c3b4c;
  color: #fff;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.projects2-card-categories {
  text-align: left;
  margin-bottom: clamp(10px, 1.2vw, 18px);
}

.projects2-card-categories.multiple-tags {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 5px;
}

/* Title styles */
.projects2-card-body .projects2-card-title h1,
.projects2-card-body .projects2-card-title h2,
.projects2-card-body .projects2-card-title h3,
.projects2-card-body .projects2-card-title h4,
.projects2-card-body .projects2-card-title h5,
.projects2-card-body .projects2-card-title h6 {
  line-height: 1.1;
  margin: 0 0 clamp(8px, 1vw, 16px) 0;
  font-weight: 800 !important;
}

.projects2-card-body .projects2-card-title h1 {
  font-size: clamp(1.5rem, 4vw, 2.25rem);
}
.projects2-card-body .projects2-card-title h2 {
  font-size: clamp(1.25rem, 2.5vw, 1.75rem);
}
.projects2-card-body .projects2-card-title h3 {
  /* font-size: clamp(1.125rem, 2vw, 1.5rem);  */
  font-size: clamp(1.25rem, 2.5vw, 1.75rem);
}
.projects2-card-body .projects2-card-title h4 {
  font-size: clamp(1rem, 1.75vw, 1.25rem);
}
.projects2-card-body .projects2-card-title h5 {
  font-size: clamp(0.875rem, 1.5vw, 1.125rem);
}
.projects2-card-body .projects2-card-title h6 {
  font-size: clamp(0.75rem, 1.25vw, 1rem);
}

.projects2-card-excerpt {
  font-size: clamp(13px, 1.2vw, 16px);
  line-height: 1.5;
  color: var(--clr-text, #6b6b6b);
  margin-bottom: clamp(14px, 2vw, 26px);
}

.projects2-card-meta {
  display: flex;
  align-items: center;
  gap: clamp(0.375rem, 1vw, 0.5rem);
  margin-top: auto;
  padding-top: clamp(6px, 1vw, 14px);
  border-top: 1px solid #f0f0f0;
}

.projects2-card-avatar {
  width: clamp(28px, 3vw, 36px);
  height: clamp(28px, 3vw, 36px);
  object-fit: cover;
  border-radius: 50%;
  margin-right: clamp(0.375rem, 1vw, 0.5rem);
  border: 2px solid #f3f3f3;
  box-shadow: 0 2px 8px 0 rgba(30, 34, 90, 0.08);
}

.projects2-card-author,
.projects2-card-date {
  font-size: clamp(11px, 1vw, 15px);
  color: #a0a0a0;
}

.projects2-card-author {
  margin-right: clamp(0.3125rem, 1vw, 0.75rem);
}

/* Media queries */
@media (max-width: 991px) {
  .projects2-card-img {
    height: clamp(80px, 18vw, 140px);
  }
}

@media (max-width: 575px) {
  .projects2-card {
    min-height: 0;
  }
  .projects2-card-img {
    height: clamp(60px, 16vw, 100px);
  }
}

/* ___________________________________ */

/* =======  Work Single style  ======= */
.work-single {
  position: relative;
}

.work-single h5 {
  font-size: clamp(
    1.5rem,
    calc(1.4rem + 0.2vw),
    1.6875rem
  ); /* Converted from 27px */
  color: #2c3b4c;
}

.work-single .s-list {
  padding: 0;
  list-style: none;
}

.work-single .s-list > li::after,
.work-single .s-list > li::before {
  content: "";
  display: table;
}

.work-single strong {
  font-family: "Poppins", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 900;
  display: block;
  color: #2c3b4c;
}

.work-single span {
  font-family: "Hind", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 400;
}

.work-single .s-list > li > :last-child {
  margin-bottom: 0;
}

.work-single .s-list > li::after {
  clear: both;
}

.work-single .s-list-divider > li:nth-child(n + 2) {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 2px solid rgba(0, 0, 0, 0.05);
}

.work-single .s-list a {
  color: #727272;
}

/* ======= Services style ======= */

/* ======= Services style with Modefied Equal Heights arnelG ======= */

/* Solution 1: Using CSS Grid (Recommended) 
.services {
  position: relative;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  align-items: stretch; /* This ensures equal heights 
}
  */

/* Solution 2: Using Flexbox (Alternative) */
.services {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.services .item {
  flex: 1 1 calc(33.333% - 20px);
  min-width: 300px;
}

.services .item {
  background-color: #f0f0f3;
  position: relative;
  border-radius: 10px;
  transition: 0.7s;
  border: none;
  display: flex; /* Make item a flex container */
  flex-direction: column; /* Stack content vertically */
  height: 100%; /* Take full height of grid cell */
}

.services .item .wrap {
  transition: 0.7s;
  text-align: center;
  padding: 75px 30px 15px 30px;
  overflow: hidden;
  flex: 1; /* Allow wrap to grow and fill available space */
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Distribute content evenly */
}

.services .item .wrap .icon-1 {
  color: var(--clr-theme-color);
  transition: 0.7s;
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  margin-bottom: 25px;
  flex-shrink: 0; /* Prevent icon from shrinking */
}

.services .item .wrap .icon-1 i {
  font-size: clamp(
    3.625rem,
    calc(3.7rem + 0.05vw),
    3.875rem
  ); /* Responsive equivalent of 60px */
  line-height: 60px;
}

.services .item .wrap .con {
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  flex: 1; /* Allow content to grow */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.services .item .wrap .con h4 {
  font-size: clamp(
    1.5rem,
    calc(1.4rem + 0.2vw),
    1.6875rem
  ); /* Converted from 24px */
  color: #2c3b4c;
  margin-bottom: 15px;
}

.services .item .wrap .con h3 {
  font-size: clamp(
    1.5rem,
    calc(1.4rem + 0.2vw),
    1.6875rem
  ); /* Converted from 24px */
  color: #2c3b4c;
  margin-bottom: 15px;
}

.services .item .wrap .con h2 {
  font-size: clamp(
    1.5rem,
    calc(1.4rem + 0.2vw),
    1.6875rem
  ); /* Converted from 24px */
  color: #2c3b4c;
  margin-bottom: 15px;
}

.services .item .wrap .con p {
  color: #7b7b7b;
  flex: 1; /* Allow paragraph to grow and push icon-2 down */
}

.services .item .wrap .con .icon-2 {
  margin-top: auto; /* Push to bottom */
  padding-top: 20px;
}

.services .item .wrap .con .icon-2 a {
  color: #fff;
  font-size: clamp(
    1.5rem,
    calc(1.4rem + 0.2vw),
    1.6875rem
  ); /* Converted from 24px */
  opacity: 0;
  transition: 0.7s;
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
}

.services .item .wrap .numb {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  font-size: clamp(
    5rem,
    calc(4.5rem + 0.8vw),
    6.25rem
  ); /* Converted from 100px and 80px */
  position: absolute;
  bottom: 10px;
  right: 15px;
  color: transparent;
  -webkit-text-stroke: 2px rgba(0, 0, 0, 0.05);
  opacity: 0.8;
  pointer-events: none; /* Prevent interference with content */
}

/* Hover and Active States */
.services .item:hover {
  background: #253b4d;
}

.services .item:hover .wrap .icon-1 {
  color: #fff;
  transform: translateY(-25px);
}

.services .item:hover .wrap .con {
  color: #fff;
  transform: translateY(-25px);
}

.services .item:hover .wrap .con h4 {
  color: #fff;
}

.services .item:hover .wrap .con h3 {
  color: #fff;
}

.services .item:hover .wrap .con p {
  color: #fff;
}

.services .item:hover .wrap .con .icon-2 a {
  color: #fff;
  opacity: 1;
}

.services .item:hover .wrap .numb {
  color: transparent;
  -webkit-text-stroke: 2px rgba(255, 255, 255, 0.2);
  opacity: 0.8;
}

.services .item.active {
  background: #253b4d;
}

.services .item.active .wrap .icon-1,
.services .item.active .wrap .con h4,
.services .item.active .wrap .con h3,
.services .item.active .wrap .con p {
  color: #fff;
}

.services .item.active .sub-icon {
  color: rgba(255, 255, 255, 0.05);
}

.services .item.active .numb {
  color: transparent;
  -webkit-text-stroke: 2px rgba(255, 255, 255, 0.2);
  opacity: 0.8;
}

.services-single h5 {
  font-size: clamp(
    1.6875rem,
    calc(1.5rem + 0.2vw),
    1.875rem
  ); /* Converted from 27px */
  color: #2c3b4c;
}

/* Responsive Design */
@media (max-width: 768px) {
  .services {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .services .item .wrap {
    padding: 50px 20px 15px 20px;
  }

  .services .item .wrap .numb {
    /* Font size handled by the main clamp rule */
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .services {
    /* grid-template-columns: repeat(2, 1fr); */
  }
}

/* ======= Services Sidebar style ======= */
.sidebar .sidebar-widget {
  position: relative;
  display: block;
  margin-bottom: 30px;
}

.sidebar .sidebar-widget:last-child {
  margin-bottom: 0px;
}

.sidebar .sidebar-widget .widget-inner {
  position: relative;
  display: block;
  padding: 45px;
  background: #f0f0f3;
  border-radius: 10px;
  margin-top: -225px;
  z-index: 9;
}

.sidebar-title {
  position: relative;
  margin-bottom: 20px;
  border-bottom: 2px solid var(--clr-theme-color);
  padding-bottom: 10px;
}

.sidebar-title h4 {
  position: relative;
  display: inline-block;
  font-size: clamp(
    1.6875rem,
    calc(1.5rem + 0.2vw),
    1.875rem
  ); /* Converted from 27px */
  color: #2c3b4c;
  margin-bottom: 0px;
}

.sidebar .services ul {
  position: relative;
  padding-left: 0;
}

.sidebar .services ul li {
  position: relative;
  line-height: 60px;
  color: #2c3b4c;
  margin-bottom: 5px;
}

.sidebar .services ul li:before {
  content: "";
  position: absolute;
  left: -20px;
  top: 0;
  right: -20px;
  bottom: 0;
  background: var(--clr-theme-color);
  border-radius: 5px;
  opacity: 0;
  transition: all 0.3s ease;
}

.sidebar .services ul li:after {
  content: "";
  position: absolute;
  left: -20px;
  top: 50%;
  margin-top: -12px;
  height: 24px;
  opacity: 0;
  transition: all 0.3s ease;
}

.sidebar .services ul li:last-child {
  margin-bottom: 0;
}

.sidebar .services ul li a {
  position: relative;
  display: block;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 400;
  color: #2c3b4c;
  transition: all 0.3s ease;
  z-index: 1;
}

.sidebar .services ul li:hover a,
.sidebar .services ul li.active a {
  color: #fff;
}

.sidebar .services ul li:hover:before,
.sidebar .services ul li.active:before,
.sidebar .services ul li:hover:after,
.sidebar .services ul li.active:after {
  opacity: 1;
}

.sidebar .services ul li a:after {
  position: absolute;
  right: 0;
  top: 0;
  content: "\f105";
  font-family: "Font Awesome 6 Pro";
  opacity: 1;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: normal;
  z-index: 1;
  font-style: normal;
}

.sidebar .services ul li a:hover:before,
.sidebar .services ul li.active a:before {
  opacity: 1;
  transform: scaleX(1);
  transition: all 0.3s ease 0.1s;
}

/* =======  Gallery Image style  ======= */
.gallery-filter {
  width: 100%;
  padding-bottom: 30px;
  padding-left: 0px;
  position: relative;
}

.gallery-filter li {
  font-family: "Poppins", sans-serif;
  font-size: clamp(
    1.1rem,
    calc(1rem + 0.2vw),
    1.25rem
  ); /* Converted from 20px */
  font-weight: 900;
  margin-right: 15px;
  display: inline-block;
  cursor: pointer;
  color: #2c3b4c;
}

.gallery-filter li:last-child {
  margin-right: 0;
}

.gallery-filter li.active {
  color: var(--clr-theme-color);
  content: "";
  left: 0;
  bottom: -4px;
  border-bottom: 2px solid var(--clr-theme-color);
  -webkit-transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  -o-transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  font-size: clamp(
    1.1rem,
    calc(1rem + 0.2vw),
    1.25rem
  ); /* Converted from 20px */
}

.gallery-filter li:hover {
  color: var(--clr-theme-color);
}

.gallery-item-inner {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-clip-path: inset(0 0 0 0);
  clip-path: inset(0 0 0 0);
  -webkit-transition: all 1.25s cubic-bezier(0.01, 0.71, 0.26, 0.94);
  -moz-transition: all 1.25s cubic-bezier(0.01, 0.71, 0.26, 0.94);
  transition: all 1.25s cubic-bezier(0.01, 0.71, 0.26, 0.94);
  border-radius: 6px;
}

.gallery-box {
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  text-align: center;
  border-radius: 10px;
}

.gallery-box .gallery-img {
  position: relative;
  overflow: hidden;
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
}

.gallery-box .gallery-img:after {
  content: " ";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.27s cubic-bezier(0.3, 0.1, 0.58, 1);
  overflow: hidden;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.gallery-box .gallery-img > img {
  transition: all 0.3s cubic-bezier(0.3, 0.1, 0.58, 1);
  border-radius: 0;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.gallery-box:hover .gallery-img > img {
  -webkit-filter: none;
  filter: none;
  -webkit-transform: scale(1.09, 1.09);
  transform: scale(1.09, 1.09);
  -webkit-transition: all 1s ease;
  -moz-transition: all 1s ease;
  -o-transition: all 1s ease;
  -ms-transition: all 1s ease;
  transition: all 1s ease;
}

.gallery-box .gallery-detail {
  position: absolute;
  opacity: 0;
  transform: translateX(-20px);
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  padding: 10px;
}

.gallery-box .gallery-detail h4 {
  font-size: clamp(
    1.25rem,
    calc(1.1rem + 0.2vw),
    1.5rem
  ); /* Converted from 24px */
  color: #fff;
  margin-bottom: 0;
}

.gallery-box .gallery-detail p {
  font-family: "Architects Daughter", cursive;
  font-size: clamp(0.9375rem, 1.875vw, 1.25rem);
  color: var(--clr-theme-color);
  margin-bottom: 0;
  font-weight: 400;
}

.gallery-box:hover .gallery-detail {
  opacity: 1;
  transform: translateX(0px);
}

.mfp-counter {
  display: none;
}

/* ======= Gallery Video style ======= */
.video-gallery {
}

.video-gallery .item {
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  text-align: center;
  border-radius: 10px;
}

.video-gallery .item:hover > img {
  transform: scale(1.1);
}

.video-gallery .item .img {
  position: relative;
  overflow: hidden;
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
}

.video-gallery .item .img:after {
  content: " ";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.27s cubic-bezier(0.3, 0.1, 0.58, 1);
  overflow: hidden;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.video-gallery .item .img > img {
  transition: all 0.3s cubic-bezier(0.3, 0.1, 0.58, 1);
  border-radius: 0;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.video-gallery .item:hover .img > img {
  -webkit-filter: none;
  filter: none;
  -webkit-transform: scale(1.09, 1.09);
  transform: scale(1.09, 1.09);
  -webkit-transition: all 1s ease;
  -moz-transition: all 1s ease;
  -o-transition: all 1s ease;
  -ms-transition: all 1s ease;
  transition: all 1s ease;
}

.video-gallery .item .text {
  position: absolute;
  opacity: 0;
  transform: translateX(-20px);
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  padding: 10px;
}

.video-gallery .item .text h4 {
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
  color: #fff;
  margin-bottom: 0;
}

.video-gallery .item .text p {
  font-family: "Architects Daughter", cursive;
  font-size: clamp(
    1.1875rem,
    calc(1.2rem + 0.05vw),
    1.3125rem
  ); /* Responsive equivalent of 20px */
  color: var(--clr-theme-color);
  margin-bottom: 0;
  font-weight: 400;
}

.video-gallery .item:hover .text {
  opacity: 1;
  transform: translateX(0px);
}

.video-gallery .item .video-icon {
  z-index: 2;
  padding-left: 5px;
  display: inline-block;
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: var(--clr-theme-color);
  border-radius: 50%;
  color: #fff;
  padding: 17px 20px 17px 20px;
  line-height: 0;
  overflow: hidden;
  transition: background-color 300ms ease, transform 300ms ease,
    color 300ms ease;
  transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
}

.video-gallery .item .video-icon:hover {
  background: #fff;
  color: #2c3b4c;
  transform: translate3d(0px, -5px, 0.01px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
}

.video-gallery .item .video-icon i {
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
}

/* ======= Popup Video style ======= */
.video-popup {
  position: relative;
  overflow: hidden;
}

.video-popup .vid {
  position: relative;
  z-index: 8;
}

.video-popup .vid .vid-butn:hover .icon {
  color: #fff;
  transform: translate3d(0px, -5px, 0.01px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
}

.video-popup .vid .vid-butn:hover .icon:before {
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
}

.video-popup .vid .vid-butn .icon {
  color: var(--clr-theme-color);
  width: 100px;
  height: 100px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  line-height: 110px;
  text-align: center;
  font-size: clamp(
    1.875rem,
    calc(1.5rem + 1.5vw),
    3.125rem
  ); /* Converted from 40px */
  position: relative;
  -webkit-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  display: inline-block;
}

.video-popup .vid .vid-butn .icon:after {
  content: "";
  position: absolute;
  top: 5px;
  bottom: 5px;
  right: 5px;
  left: 5px;
  border-radius: 50%;
  z-index: -1;
}

.video-popup .vid .vid-butn .icon:before {
  content: "";
  position: absolute;
  top: 5px;
  bottom: 5px;
  right: 5px;
  left: 5px;
  background-color: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  border-radius: 50%;
  z-index: -1;
  -webkit-transition: all 0.5s cubic-bezier(1, 0, 0, 1);
  -o-transition: all 0.5s cubic-bezier(1, 0, 0, 1);
  transition: all 0.5s cubic-bezier(1, 0, 0, 1);
}

/* ======= Accordion Box (for Faqs) style ======= */
.accordion-box {
  position: relative;
  padding: 0px;
}

.accordion-box .block {
  position: relative;
  background: #f0f0f3;
  overflow: hidden;
  margin-bottom: 15px;
  border-radius: 10px;
}

.accordion-box .block.active-block {
}

.accordion-box .block:last-child {
  margin-bottom: 0;
}

.accordion-box .block .acc-btn {
  position: relative;
  font-size: clamp(0.84375rem, 1.7vw, 1.125rem);
  line-height: 1.125rem;
  font-weight: 900;
  cursor: pointer;
  padding: 30px;
  padding-right: 60px;
  color: #2c3b4c;
  transition: all 500ms ease;
  font-family: "Poppins", sans-serif;
}

.accordion-box .block .acc-btn .count {
  color: var(--clr-theme-color);
  padding-right: 5px;
  font-weight: 900;
  font-family: "Poppins", sans-serif;
  font-size: clamp(
    1.0625rem,
    calc(1.1rem + 0.1vw),
    1.1875rem
  ); /* Responsive equivalent of 18px */
}

.accordion-box .block .acc-btn.active .count {
  color: #fff;
}

.accordion-box .block .acc-btn:before {
  position: absolute;
  right: 35px;
  top: 24px;
  height: 30px;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 900;
  color: var(--clr-theme-color);
  line-height: 30px;
  content: "\f107";
  font-family: "Font Awesome 6 Pro";
  transition: all 500ms ease;
}

.accordion-box .block .acc-btn.active {
  background-color: var(--clr-theme-color);
  color: #fff;
}

.accordion-box .block .acc-btn.active:before {
  color: #fff;
  font-family: "Font Awesome 6 Pro";
  content: "\f106";
}

.accordion-box .block .acc-content {
  position: relative;
  display: none;
}

.accordion-box .block .content {
  position: relative;
  padding: 30px;
  color: #2c3b4c;
  border-radius: 0px 0px 10px 10px;
}

.accordion-box .block .acc-content.current {
  display: block;
}

.accordion-box .block .content .text {
  display: block;
  position: relative;
  top: 0px;
  color: #727272;
}

/* ======= LetsTalk style ======= */
.lets-talk {
  position: relative;
}

.lets-talk .wrap {
  position: relative;
  background: #fff;
  padding: 45px 30px;
  overflow: hidden;
  z-index: 1;
}

.lets-talk[data-overlay-dark] h6,
.lets-talk h6 {
  display: inline-block;
  font-family: "Architects Daughter", cursive;
  font-size: clamp(
    1.1875rem,
    calc(1.2rem + 0.05vw),
    1.3125rem
  ); /* Responsive equivalent of 20px */
  color: var(--clr-theme-color);
  margin-bottom: 10px;
  font-weight: 400;
}

.lets-talk[data-overlay-dark] h5,
.lets-talk h5 {
  font-size: clamp(
    4.5rem,
    calc(4.6rem + 0.1vw),
    4.875rem
  ); /* Responsive equivalent of 75px */
  color: #fff;
  line-height: 1em;
}

.lets-talk[data-overlay-dark] h5 span,
.lets-talk h5 span {
  position: relative;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
  color: transparent;
  -webkit-text-stroke: 1px #fff;
  opacity: 0.8;
}

.lets-talk[data-overlay-dark] p,
.lets-talk p {
  font-size: clamp(
    1.0625rem,
    calc(1.1rem + 0.05vw),
    1.1875rem
  ); /* Responsive equivalent of 18px */
  color: #fff;
  line-height: 1.5em;
}

/* ======= Testimonials style ======= */
.testimonials {
  position: relative;
}

.testimonials .item {
  position: relative;
  background: #f0f0f3;
  padding: 45px 30px;
  border-radius: 10px;
  margin-bottom: 30px;
}

.testimonials .item .quote {
  position: relative;
  font-size: clamp(
    3.3rem,
    calc(3.4rem + 0.1vw),
    3.6rem
  ); /* Responsive equivalent of 55px */
  color: var(--clr-theme-color);
  line-height: 65px;
}

.testimonials .item p {
  color: #727272;
}

.testimonials .item .info {
  text-align: left;
  padding: 15px 0 0 0;
  border: none;
  border-top: 2px solid rgba(0, 0, 0, 0.05);
}

.testimonials .item .info .author-img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  float: left;
  overflow: hidden;
  border: 2px solid rgba(0, 0, 0, 0.05);
  padding: 4px;
}

.testimonials .item .info .author-img img {
  border-radius: 100%;
}

.testimonials .item .info .cont {
  margin-left: 80px;
}

.testimonials .item .info .cont h6 {
  display: block;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  margin-bottom: 0px;
  padding-top: 15px;
  font-weight: 900;
  font-family: "Poppins", sans-serif;
  color: #2c3b4c;
  line-height: 1.2em;
  text-transform: none;
  letter-spacing: 0px;
}

.testimonials .item .info span {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: #727272;
  line-height: 1.2em;
}

/* ======= Clients style ======= */
.clients {
  position: relative;
  overflow: hidden;
}

.clients .clients-logo {
  line-height: 0;
  padding: 15px;
  border: 2px solid #f0f0f3;
  position: relative;
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  overflow: hidden;
  border-radius: 5px;
  background: #f0f0f3;
}

.clients .clients-logo:hover {
  transform: scale(0.95);
  overflow: hidden;
  transition: 0.5s ease;
}

.clients img {
  -webkit-filter: none;
  filter: none;
}

/* ======= Events style ======= */
.events .item {
  padding: 30px 0;
  border-bottom: 2px solid rgba(0, 0, 0, 0.05);
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
}

.events .item:first-of-type {
  border-top: 2px solid rgba(0, 0, 0, 0.05);
}

.events .item:hover {
  border-bottom-color: var(--clr-theme-color);
}

.events .item:hover .text a {
  text-decoration: none;
}

.events .item:hover .img img {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  opacity: 1;
}

.events .item .text h5,
.events .item .text h5 a {
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
  margin-bottom: 0;
  color: #2c3b4c;
}

.events .item .text p {
  color: var(--clr-theme-color);
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 400;
  font-family: "Architects Daughter", cursive;
  margin-bottom: 0;
}

.events .item .date {
  display: flex;
  align-items: center;
}

.events .item .date > span {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  line-height: 3rem;
  font-size: clamp(
    3rem,
    calc(2.5rem + 1.5vw),
    5.3125rem
  ); /* Fluid from ~48px to 85px */
  margin-right: 12px;
  color: transparent;
  -webkit-text-stroke: 1.5px var(--clr-theme-color);
  opacity: 0.8;
}
.events .item .date div span {
  display: block;
  color: #2c3b4c;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  line-height: 16px;
  margin-top: 4px;
}

.events .item .date > h4 {
  color: #fff;
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
  font-weight: 400;
  margin-bottom: 5px;
}

.events .item .date h5 {
  color: #fff;
  font-size: clamp(
    0.8rem,
    calc(0.8rem + 0.05vw),
    0.9rem
  ); /* Responsive equivalent of 13px */
  text-transform: uppercase;
  font-weight: 400;
  font-family: "Hind", sans-serif;
  margin-bottom: 0;
}

.events .item .img {
  position: absolute;
  top: 50%;
  /*  right: -30px;*/
  right: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 250px;
  height: 250px;
  border-radius: 50%;
  overflow: hidden;
}

.events .item .img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center center;
  object-position: center center;
  border-radius: 50%;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  opacity: 0;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
}

.position-re {
  position: relative;
}

.ml-auto {
  margin-left: auto;
}

@media screen and (max-width: 768px) {
  .events .item .img {
    right: 5px;
    width: 160px;
    height: 160px;
  }

  .events .item .text h5,
  .events .item .text h5 a {
    font-size: clamp(
      1.1875rem,
      calc(1.2rem + 0.05vw),
      1.3125rem
    ); /* Responsive equivalent of 20px */
    margin-bottom: 0px;
  }
}

/* Event detail */
.event-detail .item {
  display: flex;
  padding: 30px;
  border-radius: 10px;
  background: #f0f0f3;
}

.event-detail .item:nth-child(even) {
  flex-direction: row-reverse;
}

.event-detail .item:nth-child(even) .con {
  text-align: left;
}

.event-detail .item:nth-child(even) .number {
  margin-left: unset;
  margin-right: 40px;
}

.event-detail .item .number {
  text-align: center;
  margin: 0 20px 0 0;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  padding: 0px 20px 0px 0px;
  width: 120px;
}

.event-detail .item .number h1 {
  font-size: clamp(
    4.8rem,
    calc(4.9rem + 0.1vw),
    5.1rem
  ); /* Responsive equivalent of 80px */
  line-height: 70px;
  line-height: 1;
  color: transparent;
  -webkit-text-stroke: 1.5px var(--clr-theme-color);
  opacity: 0.8;
  margin-bottom: 0;
}

.event-detail .item .number span {
  font-size: clamp(
    0.875rem,
    calc(0.9rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  font-weight: 400;
  line-height: 1;
  color: #727272;
}

.event-detail .item .con {
  margin: 10px 0;
}

.event-detail .item .con span {
  font-size: clamp(
    0.875rem,
    calc(0.9rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  font-weight: 400;
  color: #727272;
  line-height: 1;
}

.event-detail .item .con h5 {
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
  line-height: 1.2;
  margin-bottom: 5px;
  margin-top: 5px;
  color: #2c3b4c;
}

.event-detail .item .con a {
  display: block;
  color: var(--clr-theme-color);
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 400;
  font-family: "Architects Daughter", cursive;
  margin-bottom: 0;
}

/* ======= Blog Home style ======= */
.blog-home {
  overflow: hidden;
}

.blog-home .item {
  position: relative;
  margin-bottom: 15px;
}

.blog-home .item .wrap {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  text-align: center;
  height: 100%;
  width: 100%;
}

.blog-home .item .wrap .img {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 300px; /* Adjust as needed */
  border-radius: 10px;
}

.blog-home .item .wrap .img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin: 0;
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
  background-size: cover;
  opacity: 1;
}

.blog-home .item .wrap .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4); /* Semi-transparent overlay */
  border-radius: 10px;
  z-index: 1;
  opacity: 1;
  transition: opacity 0.4s ease, transform 0.4s ease;
}

.blog-home .item .wrap .title {
  position: absolute;
  bottom: 0;
  padding: 15px 30px 0 30px;
  width: 100%;
  z-index: 2;
  color: #fff;
  background-color: rgba(
    0,
    0,
    0,
    0.6
  ); /* Background for better text contrast */
  border-radius: 0 0 10px 10px;
  transition: transform 0.4s ease-in-out, color 0.4s ease-in-out;
}

.blog-home .item .wrap .title h6 a {
  font-family: "Architects Daughter", cursive;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: 700;
  color: #fff;
  display: block;
  margin-bottom: 15px;
}

.blog-home .item .wrap .title h4 a {
  color: #fff;
  font-size: clamp(0.95rem, 1vw + 0.4rem, 1.25rem); /* Responsive size */
  margin-bottom: 15px;
  line-height: 1; /* Tighter line height */
  font-weight: 800;
}

.blog-home .item .wrap .title h4.stitle a {
  font-size: clamp(0.95rem, 1vw + 0.4rem, 1.25rem);
}

.blog-home .item .wrap:hover {
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
}

.blog-home .item .wrap:hover .img img {
  opacity: 1;
  transform: scale(1.1);
}

.blog-home .item .wrap:hover .overlay {
  opacity: 0;
  transform: scale(0.95);
}

.blog-home .item .wrap:hover .title {
  transform: translateY(-35px);
  color: #ffffff;
}

.blog-home .icon-box {
  display: none;
}

/* ======= Blog 2 Grid style ======= */
.blog2 {
  position: relative;
  overflow: hidden;
}

.blog2 .item {
  margin-bottom: 15px;
  display: flex; /* Make item a flex container */
  flex-direction: column; /* Stack children vertically */
  height: 100%; /* Ensure item fills height of its parent column */
}

.blog2 .item .img {
  overflow: hidden;
  border-radius: 10px;
  height: 100%; /* You can adjust this value based on the desired height */
}

.blog2 .item .img img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Ensures the image covers the container */
  transition: transform 0.4s ease-in-out;
}

.blog2 .item .wrapper {
  background-color: #fff;
  padding: 0.1px 25px 15px 25px; /* Reduced bottom padding */
  margin: -30px 10px 0 10px;
  position: relative;
  z-index: 1;
  border-radius: 10px;
  transition: all ease 0.4s;

  display: flex; /* Make wrapper a flex container */
  flex-direction: column; /* Stack children vertically */
  flex-grow: 1; /* Allow wrapper to grow and fill height of parent item */
  height: 100%; /* Ensure wrapper takes full height of item */
  min-height: 0; /* Remove min-height to allow flex-grow to work */
}

.blog2 .item .wrapper .date {
  margin: -20px 0 20px 0;
  flex-shrink: 0; /* Prevent date from shrinking */
}

.blog2 .item .wrapper .date a {
  display: inline-block;
  font-size: clamp(
    0.875rem,
    calc(0.9rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  padding: 10.5px 20px;
  line-height: 1;
  background-color: var(--clr-theme-color);
  color: #fff;
  border-radius: 5px;
}

.blog2 .item:hover .wrapper .date a {
  background-color: #fff;
  color: #7b7b7b;
}

.blog2 .item .wrapper .con {
  overflow: hidden;
  position: relative;
  transition: all ease 0.4s;
  padding-bottom: 3rem;
  flex-grow: 1; /* Allow content to grow and fill remaining space */
  display: flex; /* Make con a flex container */
  flex-direction: column; /* Stack con's children vertically */
  justify-content: flex-start; /* Align items to the start */
}

.blog2 .item .wrapper .con .category {
  margin: 0 0 5px 0; /* Reduced bottom margin */
  flex-shrink: 0; /* Prevent category from shrinking */
}

.blog2 .item .wrapper .con .category a {
  color: #7b7b7b;
  display: inline-block;
  font-size: clamp(
    0.875rem,
    calc(0.9rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  margin-right: 10px;
}

.blog2 .item .wrapper .con .category a i {
  transition: all ease 0.4s;
  color: var(--clr-theme-color);
  margin-right: 5px;
  font-size: clamp(
    0.75rem,
    calc(0.77rem + 0.05vw),
    0.875rem
  ); /* Responsive equivalent of 12px */
}

.blog2 .item .wrapper .con .text {
  font-family: "Poppins", sans-serif;
  /* font-size: clamp(1.5rem, calc(1.3rem + 0.35vw), 1.625rem);    Responsive equivalent of 24px */
  /*font-size: clamp(1.25rem, calc(1.15rem + 0.25vw), 1.25rem);  Responsive equivalent of ~20px */
  /*font-size: clamp(1rem, 3vw, 1.375rem) !important;  ~16px to 22px */
  /* font-size: clamp(1rem, 3vw, 1.5rem) !important; ~16px to 24px */
  /*font-size: clamp(1.125rem, 4.5vw, 1.375rem) !important;  ~18px min — scales up to ~22px */
  /*font-size: clamp(1.25rem, 5vw, 1.5rem) !important;  ~20px to 24px */
  font-size: clamp(1.125rem, 2vw, 1.5rem) !important; /* 18px to 24px */
  font-weight: 800;
  margin: 0;
  line-height: 1.2em;
}

.blog2 .item .wrapper .con .text h3 {
  font-family: "Poppins", sans-serif;
  /* font-size: clamp(1.5rem, calc(1.3rem + 0.35vw), 1.625rem);    Responsive equivalent of 24px */
  /*font-size: clamp(1.25rem, calc(1.15rem + 0.25vw), 1.25rem);  Responsive equivalent of ~20px */
  /*font-size: clamp(1rem, 3vw, 1.375rem) !important;  ~16px to 22px */
  /* font-size: clamp(1rem, 3vw, 1.5rem) !important; ~16px to 24px */
  /*font-size: clamp(1.125rem, 4.5vw, 1.375rem) !important;  ~18px min — scales up to ~22px */
  /*font-size: clamp(1.25rem, 5vw, 1.5rem) !important;  ~20px to 24px */
  font-size: clamp(1.125rem, 2vw, 1.5rem) !important; /* 18px to 24px */
  font-weight: 800;
  margin: 0;
  line-height: 1.2em;
}

.blog2 .item .wrapper .con .icon-btn {
  position: absolute;
  left: 0;
  bottom: 0;
  transform: translateX(60px);
  opacity: 0;
  visibility: hidden;
  display: inline-block;
  border: none;
  padding: 0;
  width: 50px;
  height: 50px;
  line-height: 55px;
  font-size: 14px;
  background-color: #fff;
  color: #101010;
  text-align: center;
  border-radius: 50%;
  transition: all ease 0.4s;
}

.blog2 .item .wrapper .con .icon-btn:hover {
  background-color: #fff;
  color: #101010;
}

.blog2 .item:hover .wrapper {
  margin-top: -104px;
  background: #253b4d;
  border-color: transparent;
}

.blog2 .item:hover .wrapper .con {
  padding-bottom: 70px;
}

.blog2 .item:hover .wrapper .con .category a {
  color: #fff;
}

.blog2 .item:hover .wrapper .con .category a i {
  color: #fff;
}

.blog2 .item:hover .wrapper .con .category a:hover {
  color: #fff;
}

.blog2 .item:hover .wrapper .con .text a {
  color: #fff;
}

.blog2 .item:hover .wrapper .con .text a:hover {
  color: #fff;
}

.blog2 .item:hover .wrapper .con .icon-btn {
  transform: translateX(0);
  opacity: 1;
  visibility: visible;
}

/* ======= Blog & Post style ======= */
.blog .item {
  margin-bottom: 30px;
}

.blog img,
.blog .vid-icon img {
  border-radius: 5px;
}

.blog .item .img {
  position: relative;
  overflow: hidden;
  border-radius: 5px;
}

.blog .item .img:hover img {
  -webkit-filter: none;
  filter: none;
  -webkit-transform: scale(1.05, 1.05);
  transform: scale(1.05, 1.05);
  -webkit-transition: all 1s ease;
  -moz-transition: all 1s ease;
  -o-transition: all 1s ease;
  -ms-transition: all 1s ease;
  transition: all 1s ease;
}

.blog .item .img a {
  display: block;
}

.blog .item .img img {
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.blog .item .img .date {
  display: inline-block;
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
  padding: 5px 12px;
  text-align: center;
  font-family: "Hind", sans-serif;
  font-weight: 400;
  border-radius: 5px;
}

.blog .item .img .date span {
  display: block;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  line-height: 1.5em;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #fff;
  margin-bottom: 3px;
}

.blog .item .img .date i {
  font-style: normal;
  display: block;
  font-size: clamp(
    1.875rem,
    calc(1.9rem + 0.1vw),
    2rem
  ); /* Responsive equivalent of 30px */
  color: #fff;
  font-weight: 700;
}

.blog .item .wrap {
  padding: 30px 0;
}

.blog .item .wrap h4 {
  font-size: clamp(
    2rem,
    calc(2.1rem + 0.1vw),
    2.2rem
  ); /* Responsive equivalent of 32px */
  margin-bottom: 15px;
}

.blog .item .wrap h4 a:hover,
.blog .item .wrap h5 a:hover {
  color: var(--clr-theme-color);
}

/* post */
.blog .comment {
  padding-top: 60px;
  margin-bottom: 60px;
}

.blog .comment h5 {
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
  color: #2c3b4c;
}

.blog .comment h5 span {
  font-family: "Hind", sans-serif;
  font-size: clamp(
    0.875rem,
    calc(0.9rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  color: #727272;
  font-weight: 400;
  margin-left: 15px;
}

.blog .comment .wrap {
  display: flex;
  margin-bottom: 60px;
}

.blog .comment .img img {
  margin-right: 30px;
  flex: 0 0 auto;
  border-radius: 100%;
  width: 100px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  padding: 4px;
}

.blog .comment .text {
  margin-right: 20px;
}

.blog .repay {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: #fff;
  margin: 0;
  font-weight: 400;
}

.blog .category {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 10px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  color: #727272;
  font-weight: 400;
  font-family: "Hind", sans-serif;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
}

.blog .category a {
  color: var(--clr-theme-color);
}

.blog .category span {
  color: #727272;
}

.blog .divider {
  width: 20px;
  height: 2px;
  margin-right: 15px;
  margin-left: 15px;
  background-color: var(--clr-theme-color);
}

/* sidebar */
.blog-sidebar .item {
  position: relative;
  background: #f0f0f3;
  padding: 45px 30px;
  margin-bottom: 30px;
  display: grid;
  border-radius: 10px;
}

.blog-sidebar .item ul {
  margin-bottom: 0;
  padding: 0;
}

.blog-sidebar .item ul li {
  margin-bottom: 15px;
  color: #2c3b4c;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  line-height: 1.5em;
}

.blog-sidebar .item ul li a {
  color: #727272;
}

.blog-sidebar .item ul li a.active {
  color: var(--clr-theme-color);
}

.blog-sidebar .item ul li a:hover {
  color: var(--clr-theme-color);
}

.blog-sidebar .item ul li:last-child {
  margin-bottom: 0;
}

.blog-sidebar .item ul li i {
  font-size: clamp(0.75rem, 0.75rem, 0.75rem); /* Converted from 12px */
  margin-right: 10px;
  color: var(--clr-theme-color);
}

.blog-sidebar .item .recent li {
  display: block;
  overflow: hidden;
}

.blog-sidebar .item .recent .thum {
  width: 90px;
  overflow: hidden;
  float: left;
}

.blog-sidebar .item .recent a {
  display: block;
  margin-left: 105px;
}

.blog-sidebar .item .text {
  margin-bottom: 5px;
}

.blog-sidebar .item .text h5 {
  font-size: clamp(
    1.6875rem,
    calc(1.5rem + 0.2vw),
    1.875rem
  ); /* Converted from 27px */
  color: #2c3b4c;
}

.blog-sidebar .item .search form {
  position: relative;
}

.blog-sidebar .item .search form input {
  width: 100%;
  padding: 15px;
  border: 0;
  background: #fff;
  margin-bottom: 0;
  height: 61px;
  border-radius: 30px;
  color: #2c3b4c;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
}

.blog-sidebar .item .search form button {
  position: absolute;
  right: 5px;
  top: 5px;
  background-color: var(--clr-theme-color);
  color: #fff;
  border: 0;
  padding: 16px 17px 14px 17px;
  cursor: pointer;
  border-radius: 100%;
  transform: rotate(0);
}

.blog-sidebar .item .search:hover form button {
  background-color: #2c3b4c;
  color: #fff;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
  -webkit-transition: all 0.7s linear;
  -o-transition: all 0.7s linear;
  transition: all 0.7s linear;
}

.blog-sidebar .tags li {
  margin: 5px !important;
  padding: 5px 25px;
  background-color: transparent;
  border: none;
  color: #727272 !important;
  float: left;
  border: 2px solid var(--clr-theme-color);
  border-radius: 30px;
}

.blog-sidebar .item ul.tags li a {
  font-size: clamp(
    0.875rem,
    calc(0.9rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  color: #727272;
}

.blog-sidebar .item ul.tags li:hover,
.blog-sidebar .item ul.tags li:hover a {
  background-color: var(--clr-theme-color);
  color: #fff;
}

/* pagination */
.pagination-wrap {
  padding: 0;
  margin: 0;
}

.pagination-wrap li {
  display: inline-block;
  margin: 0 5px;
}

.pagination-wrap li a {
  background: #f0f0f3;
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 46px;
  text-align: center;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  border: 2px solid #f0f0f3;
  border-radius: 50%;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
}

.pagination-wrap li a i {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
}

.pagination-wrap li a:hover {
  opacity: 1;
  text-decoration: none;
  background: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}

.pagination-wrap li a.active {
  background-color: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}

@media screen and (max-width: 768px) {
  .pagination-wrap {
    padding: 0;
    margin: 0;
    text-align: center;
  }
}

/* ======= Contact style ======= */
.contact h5 {
  font-size: clamp(
    1.875rem,
    calc(1.9rem + 0.1vw),
    2rem
  ); /* Converted from 30px */
  color: #2c3b4c;
  margin-bottom: 30px;
}

.contact .item {
  padding: 15px 0px;
}

.contact .item .wrap-block {
  padding: 0;
  position: relative;
  overflow: hidden;
  margin-bottom: 15px;
}

.contact .item .icon {
  width: 50px;
  height: 50px;
  line-height: 47px;
  font-size: clamp(
    1.5rem,
    calc(1.4rem + 0.2vw),
    1.6875rem
  ); /* Converted from 24px */
  border-radius: 100%;
  margin-right: 15px;
  text-align: center;
  position: relative;
  z-index: 3;
  float: left;
  border: 2px solid var(--clr-theme-color);
  font-weight: normal;
}

.contact .item .text-block {
  overflow: hidden;
}

.contact .item h5 {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  margin-bottom: 0px;
  margin-top: 0px;
  color: #fff;
}

.contact .item p {
  margin-bottom: 0px;
}

.google-map {
  width: 100%;
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
  height: calc(100% + 30px);
  overflow: hidden;
}

.contact .con {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: #2c3b4c;
  text-decoration: none;
}

.contact .con .icon {
  width: 50px;
  height: 50px;
  line-height: 48px;
  margin-right: 15px;
  margin-bottom: 10px;
  border-radius: 100%;
  border: 2px solid var(--clr-theme-color);
  text-align: center;
  background: var(--clr-theme-color);
  font-size: clamp(
    1.125rem,
    calc(1.05rem + 0.1vw),
    1.25rem
  ); /* Responsive equivalent of 18px */
  color: #fff;
}

.contact .con .icon .img {
  border-radius: 0;
  border: 2px solid var(--clr-theme-color);
}

.contact .con .icon img {
  background: transparent;
  padding: 13px;
}

.contact .con .text {
  color: #2c3b4c;
  line-height: 1.5em;
  margin-top: 15px;
}

/* opening-hours list */
.opening-hours ul {
  margin: 0;
  padding: 0;
  margin-bottom: 30px;
  width: 320px;
}

.opening-hours ul li .tit,
.opening-hours ul li .dots,
.opening-hours ul li span {
  display: table-cell;
  white-space: nowrap;
}

.opening-hours ul li {
  margin-bottom: 15px;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
}

.opening-hours ul li:last-of-type {
  margin-bottom: 0;
}

.opening-hours ul li span {
  white-space: nowrap;
  padding-left: 10px;
  color: #727272;
}

.opening-hours ul li .tit {
  padding-right: 10px;
  color: #727272;
}

.opening-hours ul li .dots {
  height: 4px;
  width: 100%;
  border-bottom: 2px solid rgba(0, 0, 0, 0.05);
  position: relative;
  top: -7px;
}

/* ======= 404 style ======= */
.not-found h2 {
  font-size: 180px;
  line-height: 180px;
  font-family: "Poppins", sans-serif;
  color: var(--clr-theme-color);
  margin-bottom: 15px;
  font-weight: 900;
}

.not-found p {
  color: #727272;
}

/* ======= Button style ======= */
/* button 1 */
.btn-1 {
  display: inline-block;
  height: auto;
  padding: clamp(0.8rem, 1vw + 0.6rem, 1.2rem)
    clamp(1.5rem, 2vw + 0.8rem, 2.5rem);
  border: 2px solid var(--clr-theme-color);
  border-radius: 80px;
  background-color: var(--clr-theme-color);
  -webkit-transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  color: #fff;
  line-height: 1.2;
  text-align: center;
  margin-right: 10px;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-size: clamp(0.9rem, calc(1vw + 0.3rem), 1.25rem);
  border: 1px solid transparent;
}

.btn-1:hover {
  border: 2px solid #fff;
  background-color: #fff;
  color: #2c3b4c;
  border: 1px solid #2c3b4c;
}

.btn-1 i {
  color: #fff;
  font-size: clamp(
    1.1875rem,
    calc(1.2rem + 0.05vw),
    1.3125rem
  ); /* Responsive equivalent of 20px */
  margin-right: 5px;
}

.btn-1:hover i {
  color: #2c3b4c;
}

/* button 2 */
.btn-2 {
  display: inline-block;
  height: auto;
  padding: clamp(0.8rem, 1vw + 0.6rem, 1.2rem)
    clamp(1.5rem, 2vw + 0.8rem, 2.5rem);
  border: 1px solid #2c3b4c;
  border-radius: 80px;
  background-color: #2c3b4c;
  -webkit-transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  color: #fff;
  line-height: 1.2;
  text-align: center;
  margin-right: 10px;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-size: clamp(0.9rem, calc(1vw + 0.3rem), 1.25rem);
}

.btn-2:hover {
  border: 1px solid #fff;
  background-color: #fff;
  color: #2c3b4c;
}

.btn-2 i {
  color: #fff;
  font-size: clamp(
    1.1875rem,
    calc(1.2rem + 0.05vw),
    1.3125rem
  ); /* Responsive equivalent of 20px */
  margin-right: 5px;
}

.btn-2:hover i {
  color: #2c3b4c;
}

/* button 3 */
.btn-3 {
  display: inline-block;
  height: auto;
  padding: clamp(0.8rem, 1vw + 0.6rem, 1.2rem)
    clamp(1.5rem, 2vw + 0.8rem, 2.5rem);
  border: 1px solid var(--clr-theme-color);
  border-radius: 80px;
  background-color: var(--clr-theme-color);
  -webkit-transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  color: #fff;
  line-height: 1.2;
  text-align: center;
  margin-right: 10px;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-size: clamp(0.9rem, calc(1vw + 0.3rem), 1.25rem);
}

.btn-3 i {
  color: #fff;
  font-size: clamp(
    1.1875rem,
    calc(1.2rem + 0.05vw),
    1.3125rem
  ); /* Responsive equivalent of 20px */
  margin-right: 5px;
}

.btn-3:hover {
  border: 1px solid #2c3b4c;
  background-color: #2c3b4c;
  color: #fff;
}

/* button 4 */
.btn-4 {
  display: inline-block;
  height: auto;
  padding: clamp(0.8rem, 1vw + 0.6rem, 1.2rem)
    clamp(1.5rem, 2vw + 0.8rem, 2.5rem);
  border: 1px solid #2c3b4c;
  border-radius: 80px;
  background-color: #2c3b4c;
  -webkit-transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  color: #fff;
  line-height: 1.2;
  text-align: center;
  margin-right: 10px;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-size: clamp(0.9rem, calc(1vw + 0.3rem), 1.25rem);
}

.btn-4 i {
  color: #fff;
  font-size: clamp(
    1.1875rem,
    calc(1.2rem + 0.05vw),
    1.3125rem
  ); /* Responsive equivalent of 20px */
  margin-right: 5px;
}

.btn-4:hover {
  border: 1px solid var(--clr-theme-color);
  background-color: var(--clr-theme-color);
  color: #fff;
}

/* button 5 */
.btn-5 {
  display: inline-block;
  height: auto;
  padding: clamp(0.8rem, 1vw + 0.6rem, 1.2rem)
    clamp(1.5rem, 2vw + 0.8rem, 2.5rem);
  border: 1px solid #fff;
  border-radius: 80px;
  background-color: #fff;
  -webkit-transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  color: #2c3b4c;
  line-height: 1.2;
  text-align: center;
  margin-right: 10px;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-size: clamp(0.9rem, calc(1vw + 0.3rem), 1.25rem);
}

.btn-5:hover {
  border: 1px solid var(--clr-theme-color);
  background-color: var(--clr-theme-color);
  color: #fff;
}

.btn-5 i {
  color: #2c3b4c;
  font-size: clamp(
    1.1875rem,
    calc(1.2rem + 0.05vw),
    1.3125rem
  ); /* Responsive equivalent of 20px */
  margin-right: 5px;
}

.btn-5:hover i {
  color: #fff;
}

@media (max-width: 600px) {
  /* Button Group Styles */
  .btn-group {
    flex-wrap: wrap;
    gap: 1rem;
    display: flex;
    justify-content: center;
  }
  .btn-group .btn-1,
  .btn-group .btn-2,
  .btn-group .btn-3,
  .btn-group .btn-4,
  .btn-group .btn-5 {
    text-align: center;
  }
}

/* ======= Footer style ======= */
.footer {
  position: relative;
  background-color: #2c3b4c;
}

/* top */
.footer .top {
  position: relative;
  display: block;
  padding: 120px 0 60px 0;
  z-index: 1;
  background-color: #101010;
  /* border-top: 1px solid rgba(186, 123, 34, 0.5); */
  border-top: 2px solid rgba(255, 255, 255, 0.07);
}

.footer .top img {
  width: 150px;
  margin-bottom: 15px;
}

.footer .top h4 {
  font-family: "Poppins", sans-serif;
  font-size: clamp(
    2.5rem,
    calc(2.25rem + 0.5vw),
    2.75rem
  ); /* Responsive equivalent of 40px */
  font-weight: 900;
  display: block;
  width: 100%;
  position: relative;
  color: #fff;
  line-height: 1.2em;
}

.footer .top h4 span {
  color: var(--clr-theme-color);
}

.footer .top h5 {
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
  color: #fff;
  margin-bottom: 15px;
}

.footer .top p {
  color: #999;
}

.footer .top .social {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.footer .top .social a {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: clamp(
    1.125rem,
    calc(1.05rem + 0.1vw),
    1.25rem
  ); /* Responsive equivalent of 18px */
  border: 2px solid var(--clr-theme-color);
  color: #fff;
  border-radius: 0;
  margin-right: 5px;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  background: transparent;
  border-radius: 100%;
  overflow: hidden;
  transition: background-color 300ms ease, transform 300ms ease,
    color 300ms ease;
  transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
}

.footer .top .social a:hover {
  border: 2px solid var(--clr-theme-color);
  background: var(--clr-theme-color);
  color: #fff;
  transform: translate3d(0px, -5px, 0.01px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
}

.footer .top .social a + a {
  margin-left: 5px;
}

.footer .top .phone {
  font-size: clamp(
    1.5rem,
    calc(1.3rem + 0.35vw),
    1.625rem
  ); /* Responsive equivalent of 24px */
  color: var(--clr-theme-color);
  font-weight: 600;
  display: block;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  font-family: "Poppins", sans-serif;
}

.footer .top .phone a {
  color: var(--clr-theme-color);
}

.footer .top .phone a:hover .footer .top .phone:hover {
  color: var(--clr-theme-color);
}

.footer .top .mail {
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  line-height: 3em;
  color: #fff;
  position: relative;
  display: inline-block;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.footer .top .mail a {
  color: #fff;
}

.footer .top .mail a:hover .footer .top .mail:hover {
  color: #fff;
}

.footer .top .mail:before {
  position: absolute;
  bottom: 9px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--clr-theme-color);
  content: "";
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.footer .top .mail:hover:before {
  background-color: var(--clr-theme-color);
}

/* bottom */
.footer .bottom {
  position: relative;
  display: block;
  background-color: #101010;
  padding: 30px 0;
  border: none;
  border-top: 2px solid rgba(255, 255, 255, 0.05);
}

.footer .bottom p {
  font-size: clamp(
    0.875rem,
    calc(0.9rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  color: #999;
  margin-bottom: 5px;
}

.footer .bottom p a {
  color: #fff;
  font-size: clamp(
    0.875rem,
    calc(0.9rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  line-height: 3em;
  position: relative;
  display: inline-block;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.footer .bottom p a:hover {
  color: #fff;
}

.footer .bottom p a:before {
  position: absolute;
  bottom: 9px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--clr-theme-color);
  content: "";
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.footer .bottom p a:hover:before {
  background-color: var(--clr-theme-color);
}

.footer .bottom .horizontal-link li {
  display: inline-block;
  margin: 0 25px 5px 0 !important;
}

.footer .bottom .horizontal-link li a {
  font-size: clamp(
    0.875rem,
    calc(0.9rem + 0.05vw),
    1rem
  ); /* Responsive equivalent of 14px */
  color: #999;
}

/* list */
.footer .list li + li {
  margin-top: 5px;
}

.footer .list li a {
  position: relative;
  display: block;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  color: #999;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  padding-left: 15px;
}

.footer .list li a:hover {
  padding-left: 15px;
  color: var(--clr-theme-color);
}

.footer .list li a:before {
  position: absolute;
  top: 45%;
  left: 0;
  bottom: 0;
  width: 7px;
  height: 7px;
  content: "";
  background-color: var(--clr-theme-color);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  border-radius: 50%;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.footer .list li a:hover:before {
  opacity: 1;
}

/* subscribe */
.subscribe form {
  margin-top: 30px;
  position: relative;
}

.subscribe form input {
  width: 100%;
  padding: 16px 60px 17px 16px;
  background-color: transparent;
  border: 2px solid var(--clr-theme-color);
  border-radius: 30px;
  color: #fff;
  margin-bottom: 15px;
  outline: none;
}

.subscribe form input::placeholder {
  color: #999;
}

.subscribe form button {
  background-color: #fff;
  padding: 16px 17px 14px 17px;
  border-radius: 100%;
  color: var(--clr-theme-color);
  border: 0;
  position: absolute;
  top: 5.25px;
  right: 6px;
  cursor: pointer;
  outline: none;
  transform: rotate(0);
}

.subscribe:hover form button {
  background-color: #fff;
  color: var(--clr-theme-color);
  outline: none;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: all 0.7s linear;
  -o-transition: all 0.7s linear;
  transition: all 0.7s linear;
}

@media (max-width: 768px) {
  .footer .bottom {
    text-align: center;
  }

  .footer .copyrights.full-width p {
    text-align: center;
  }

  .footer .copyrights.full-width {
    text-align: center;
  }

  .footer .bottom p {
    text-align: center;
  }
}

/* ======= toTop Button style ======= */
.progress-wrap {
  position: fixed;
  bottom: 30px;
  right: 30px;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateY(20px);
  -ms-transform: translateY(20px);
  transform: translateY(20px);
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
  -webkit-box-shadow: inset 0 0 0 2px 2px rgba(0, 0, 0, 0.03);
  box-shadow: inset 0 0 0 2px 2px rgba(0, 0, 0, 0.03);
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}

.progress-wrap::after {
  position: absolute;
  font-family: "Font Awesome 6 Pro";
  content: "\f062";
  text-align: center;
  line-height: 50px;
  font-size: clamp(
    1rem,
    calc(0.875rem + 0.3vw),
    1.125rem
  ); /* Responsive equivalent of 16px */
  font-weight: bold;
  left: 0;
  top: 0;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  z-index: 1;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
}

.progress-wrap svg path {
  fill: none;
}

.progress-wrap svg.progress-circle path {
  stroke: var(--clr-theme-color);
  stroke-width: 4;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
}

.progress-wrap::after {
  color: var(--clr-theme-color);
}

.progress-wrap svg.progress-circle path {
  stroke: var(--clr-theme-color);
}

/* ======= Media Query style ======= */

@media (max-width: 767px) {
  .parallax-header h1,
  .parallax-header h1 span,
  .parallax-header[data-overlay-dark] h1,
  .parallax-header[data-overlay-dark] h1 span,
  .not-found h1,
  .header .caption h1,
  .header .caption h1 span,
  .video-fullscreen h1,
  .events .item .date > span {
    font-size: clamp(
      3.75rem,
      calc(3.25rem + 0.6vw),
      3.875rem
    ); /* Responsive equivalent of 60px */
    line-height: 1.2em;
  }

  .section-padding h1 {
    font-size: clamp(
      3rem,
      calc(2.7rem + 0.6vw),
      3.25rem
    ); /* Responsive equivalent of 48px */
  }

  .section-padding[data-overlay-dark] h6,
  .section-padding h6 {
    font-size: clamp(
      1.125rem,
      calc(1.05rem + 0.1vw),
      1.25rem
    ); /* Responsive equivalent of 18px */
  }

  .section-padding h2 {
    font-size: 32px;
  }

  .not-found h2 {
    font-size: clamp(
      6rem,
      calc(5.5rem + 2.8vw),
      7.5rem
    ); /* Responsive equivalent of 120px */
    height: 140px;
  }

  .lets-talk[data-overlay-dark] h5,
  .lets-talk h5 {
    font-size: clamp(
      3.75rem,
      calc(3.25rem + 0.6vw),
      3.875rem
    ); /* Responsive equivalent of 60px */
  }

  .sidebar .sidebar-widget .widget-inner {
    margin-top: 0px;
  }

  .footer .bottom p {
    text-align: left;
  }

  .blog .item .wrap h4 {
    font-size: clamp(
      1.5rem,
      calc(1.4rem + 0.2vw),
      1.6875rem
    ); /* Responsive equivalent of 27px */
  }

  .testimonials .wrap {
    padding: 40px 20px;
    margin-bottom: 15px;
  }

  .accordion-box .block .acc-btn {
    padding: 25px 30px 25px 15px;
    font-family: "Hind", sans-serif;
    font-size: clamp(
      1rem,
      calc(0.875rem + 0.3vw),
      1.125rem
    ); /* Responsive equivalent of 16px */
    font-weight: 400;
    line-height: 24px;
  }

  .accordion-box .block .acc-btn:before {
    right: 15px;
  }

  .pagination-wrap {
    margin-bottom: 30px;
  }
}

@media (max-width: 991px) {
  .sidebar .sidebar-widget .widget-inner {
    margin-top: 0px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1190px !important;
  }
}

/* ======= Overlay Effect Bg image ======= */
[data-overlay-dark] {
  position: relative;
}

[data-overlay-dark] .container {
  position: relative;
  z-index: 2;
}

[data-overlay-dark]:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

[data-overlay-dark]:before {
  background: #000;
}

[data-overlay-dark] h1,
[data-overlay-dark] h2,
[data-overlay-dark] h3,
[data-overlay-dark] h4,
[data-overlay-dark] h5,
[data-overlay-dark] h6 {
  color: #fff;
}

[data-overlay-dark] p {
  color: #2c3b4c;
}

[data-overlay-dark="0"]:before {
  opacity: 0;
}

[data-overlay-dark="1"]:before {
  opacity: 0.1;
}

[data-overlay-dark="2"]:before {
  opacity: 0.2;
}

[data-overlay-dark="3"]:before {
  opacity: 0.3;
}

[data-overlay-dark="4"]:before {
  opacity: 0.4;
}

[data-overlay-dark="5"]:before {
  opacity: 0.5;
}

[data-overlay-dark="6"]:before {
  opacity: 0.6;
}

[data-overlay-dark="7"]:before {
  opacity: 0.7;
}

[data-overlay-dark="8"]:before {
  opacity: 0.8;
}

[data-overlay-dark="9"]:before {
  opacity: 0.9;
}

[data-overlay-dark="10"]:before {
  opacity: 1;
}

/*
 * [Widget: Projects Home Parallax Image4]
 * Ensure card title (h3) is bold for this widget only.
 * This rule uses high specificity and !important to override any other styles.
 * If the title tag is not h3, adjust the selector accordingly.
 */
.works-image4 .projects2-card-title h3 {
  font-weight: 900 !important;
}
