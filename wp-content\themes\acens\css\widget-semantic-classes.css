/*
================================================================
Widget Semantic CSS Classes
================================================================

This file provides CSS class-based styling for Elementor widgets to enable
semantic HTML tag flexibility without visual impact. Users can change HTML
tags (H1→H2, H3→DIV, etc.) for SEO optimization while maintaining exact
visual appearance.

Implementation Date: 2025-06-21
Purpose: Visual-semantic separation for 40 Elementor widgets
================================================================
*/

/* ======= High Priority: Slider/Header Widgets ======= */

/* Slider Parallax Image Widget - Enhanced Desktop Proportions */
.slider-parallax-subheading {
  display: block;
  font-weight: 400;
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.25rem); /* Enhanced desktop proportion: 16px-20px for better visual hierarchy */
  color: var(--clr-theme-color);
  margin-bottom: 15px;
  text-transform: none; /* Clean implementation - no uppercase by default */
  letter-spacing: 3px;
  line-height: 1.4em; /* Improved readability */
}

.slider-parallax-heading {
  position: relative;
  font-size: clamp(4rem, calc(4.5rem + 1vw), 6.875rem); /* Responsive equivalent of 110px */
  line-height: 1.1; /* Responsive line-height for better scaling */
  letter-spacing: 3px;
  font-weight: 900;
  color: #fff;
  margin-bottom: 15px;
  text-transform: none; /* Clean implementation - no uppercase by default */
}

.slider-parallax-heading span {
  position: relative;
  font-size: clamp(6.25rem, calc(5.5rem + 0.5vw), 6.5rem); /* Responsive equivalent of 100px */
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
  color: transparent;
  -webkit-text-stroke: 1px #fff; /* Standardized stroke color for consistency */
  opacity: 0.8;
  line-height: 1.1; /* Consistent line-height with main heading */
}

/* Banner Header Widgets */
.banner-header-heading {
  font-size: clamp(2.25rem, calc(1.978rem + 0.085vw), 3rem); /* Converted from 48px and 36px */
  color: #fff;
  position: relative;
  line-height: 1.25em;
  margin-bottom: 0;
  font-weight: 900;
  font-family: "Poppins", sans-serif;
}

/* ======= High Priority: Section Header Widgets ======= */

/* Section Header Primary (Main Heading) */
.section-header-heading {
  font-size: clamp(2rem, 2.625vw + 1rem, 2.625rem); /* Standardized responsive font size */
  font-weight: 900;
  margin-bottom: 15px;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  line-height: 1.25em;
}

.section-header-heading.color-dark {
  color: #2c3b4c;
}

.section-header-heading.color-white {
  color: #fff;
}

/* Override original CSS selectors with higher specificity */
.section-padding .section-header-heading {
  font-size: clamp(2rem, 2.625vw + 1rem, 2.625rem) !important;
  font-weight: 900 !important;
  margin-bottom: 15px !important;
  color: #2c3b4c !important;
  font-family: "Poppins", sans-serif !important;
  line-height: 1.25em !important;
}

/* Specific overrides for different heading tags */
.section-padding h1.section-header-heading,
.section-padding h2.section-header-heading,
.section-padding h3.section-header-heading,
.section-padding h4.section-header-heading,
.section-padding h5.section-header-heading,
.section-padding h6.section-header-heading {
  font-size: clamp(2rem, 2.625vw + 1rem, 2.625rem) !important;
  font-weight: 900 !important;
  margin-bottom: 15px !important;
  color: #2c3b4c !important;
  font-family: "Poppins", sans-serif !important;
  line-height: 1.25em !important;
}

section.lets-talk h5.section-header-heading {
  font-size: clamp(4.5rem, calc(4.6rem + 0.1vw), 4.875rem) !important;
  color: #fff !important; 
  line-height: 1em !important;
}

/* Section Header Secondary - Base Class (No uppercase, no underline by default) */
.section-header-subheading {
  display: block; /* Fixed display property for consistent layout flow */
  font-family: "Architects Daughter", cursive;
  font-size: clamp(1.125rem, calc(1.05rem + 0.1vw), 1.25rem); /* Responsive equivalent of 18px */
  font-weight: 400;
  color: var(--clr-theme-color);
  margin-bottom: 15px;
  letter-spacing: 3px;
  position: relative;
}

/* Section Header Subheading with Uppercase and Underline (for specific contexts) */
.section-header-subheading.with-underline {
  /* text-transform: uppercase; */
}

.section-header-subheading.with-underline::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 60px;
  height: 1px;
  background: var(--clr-theme-color);
  transform: translateX(-50%);
}

/* Override original CSS selectors with higher specificity */
.section-padding .section-header-subheading {
  display: block !important; /* Fixed display property for consistent layout flow */
  font-family: "Architects Daughter", cursive !important;
  font-size: clamp(1.125rem, calc(1.05rem + 0.1vw), 1.25rem) !important;
  font-weight: 400 !important;
  color: var(--clr-theme-color) !important;
  margin-bottom: 15px !important;
  letter-spacing: 3px !important;
  text-transform: none !important; /* Override any uppercase from original selectors */
}

/* Specific override for section-padding to ensure our classes take precedence */
.section-padding h1.section-header-subheading,
.section-padding h2.section-header-subheading,
.section-padding h3.section-header-subheading,
.section-padding h4.section-header-subheading,
.section-padding h5.section-header-subheading,
.section-padding h6.section-header-subheading {
  display: block !important; /* Fixed display property for consistent layout flow */
  font-family: "Architects Daughter", cursive !important;
  font-size: clamp(1.125rem, calc(1.05rem + 0.1vw), 1.25rem) !important;
  font-weight: 400 !important;
  color: var(--clr-theme-color) !important;
  margin-bottom: 15px !important;
  letter-spacing: 3px !important;
  /* text-transform: none !important;  Ensure no uppercase unless specifically requested */
}

/* Section Description */
.section-header-description {
  font-family: "Hind", sans-serif;
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem); /* Responsive equivalent of 16px */
  line-height: 1.75em;
  margin: 0 0 15px;
  color: #727272;
  font-weight: 400;
}

/* ======= Medium Priority: About Widgets ======= */

/* About Widget Headings */
.about-item-title {
  color: #fff;
  font-size: clamp(1.25rem, calc(1.1rem + 0.2vw), 1.5rem); /* Converted from 24px */
  margin-bottom: 5px;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  line-height: 1.25em;
}

.about-item-subtitle {
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem); /* Responsive equivalent of 16px */
  font-family: "Didact Gothic", sans-serif;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
  margin-bottom: 15px;
  text-transform: none; /* Clean implementation - no uppercase */
  letter-spacing: normal; /* Standardized letter spacing */
}

/* Override for about item subtitle to ensure proper styling */
.about .item .wrap .title h1.about-item-subtitle,
.about .item .wrap .title h2.about-item-subtitle,
.about .item .wrap .title h3.about-item-subtitle,
.about .item .wrap .title h4.about-item-subtitle,
.about .item .wrap .title h5.about-item-subtitle,
.about .item .wrap .title h6.about-item-subtitle,
h1.about-item-subtitle,
h2.about-item-subtitle,
h3.about-item-subtitle,
h4.about-item-subtitle,
h5.about-item-subtitle,
h6.about-item-subtitle {
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem) !important;
  font-family: "Didact Gothic", sans-serif !important;
  font-weight: 400 !important;
  color: rgba(255, 255, 255, 0.7) !important;
  position: relative !important;
  margin-bottom: 15px !important;
  /* text-transform: none !important; */
  letter-spacing: 0 !important;
}

.about-content-heading {
  font-size: clamp(1.25rem, calc(1.1rem + 0.2vw), 1.5rem); /* Converted from 24px */
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  line-height: 1.25em;
  margin-bottom: 15px;
}

/* About Box Widget */
.about-box-heading {
  font-size: clamp(1.25rem, calc(1.1rem + 0.2vw), 1.5rem); /* Converted from 24px */
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  line-height: 1.25em;
  margin-bottom: 15px;
}

/* ======= Medium Priority: Team Widgets ======= */

/* Team Member Name */
.team-member-name {
  font-size: clamp(1.125rem, calc(1rem + 0.3vw), 1.25rem); /* Responsive equivalent of 18px */
  color: #2c3b4c;
  line-height: 1.5em;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  margin-bottom: 5px;
}

.team-member-name a {
  color: #2c3b4c;
  text-decoration: none;
}

/* Team Member Position */
.team-member-position {
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem); /* Responsive equivalent of 16px */
  color: #727272;
  text-transform: none;
  font-weight: 400;
  font-family: "Hind", sans-serif;
  margin-bottom: 15px;
}

/* Override for team member position to ensure no uppercase */
.team .item .con h1.team-member-position,
.team .item .con h2.team-member-position,
.team .item .con h3.team-member-position,
.team .item .con h4.team-member-position,
.team .item .con h5.team-member-position,
.team .item .con h6.team-member-position,
h1.team-member-position,
h2.team-member-position,
h3.team-member-position,
h4.team-member-position,
h5.team-member-position,
h6.team-member-position {
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem) !important;
  color: #727272 !important;
  /* text-transform: none !important; */
  font-weight: 400 !important;
  font-family: "Hind", sans-serif !important;
  margin-bottom: 15px !important;
  letter-spacing: 0 !important;
}

/* Team Single Page */
.team-single-info-label {
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem); /* Responsive equivalent of 16px */
  font-weight: 900;
  line-height: 1.2em;
  white-space: normal;
  margin: 0;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
}

.team-single-info-value {
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem); /* Responsive equivalent of 16px */
  font-weight: 900;
  white-space: normal;
  margin: 0;
  margin-top: 0;
  color: #727272;
  font-family: "Hind", sans-serif;
}

/* ======= Medium Priority: Blog Widgets ======= */

/* Blog Post Title */
.blog-post-title {
  font-size: clamp(1.125rem, 2vw, 1.5rem) !important; /* Responsive equivalent of 32px */
  margin-bottom: 15px;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  line-height: 1.25em;
}

.blog-post-title a {
  color: #2c3b4c;
  text-decoration: none;
}

.blog-post-title a:hover {
  color: var(--clr-theme-color);
}

/* Blog Home Widget */
.blog-home-category {
  font-family: "Architects Daughter", cursive;
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem); /* Responsive equivalent of 16px */
  font-weight: 700;
  color: #fff;
  display: block;
  margin-bottom: 10px;
}

.blog-home-category a {
  color: #fff;
  text-decoration: none;
}

.blog-home-title {
  color: #fff;
  font-size: clamp(0.95rem, 1vw + 0.4rem, 1.25rem); /* Responsive size */
  margin-bottom: 15px;
  line-height: 1; /* Tighter line height */
  font-weight: 800;
  font-family: "Poppins", sans-serif;
}

.blog-home-title a {
  color: #fff;
  text-decoration: none;
}

.blog-home-title.small-title {
  font-size: clamp(0.95rem, 1vw + 0.4rem, 1.25rem);
}

/* Blog Comment Section */
.blog-comment-heading {
  font-size: clamp(1.5rem, calc(1.3rem + 0.35vw), 1.625rem); /* Responsive equivalent of 24px */
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  line-height: 1.25em;
  margin-bottom: 15px;
}

/* Blog Sidebar */
.blog-sidebar-title {
  font-size: clamp(1.6875rem, calc(1.5rem + 0.2vw), 1.875rem); /* Converted from 27px */
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  line-height: 1.25em;
  margin-bottom: 15px;
}

/* ======= Low Priority: Testimonial Widgets ======= */

/* Testimonial Author Name */
.testimonial-author-name {
  display: block;
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem); /* Responsive equivalent of 16px */
  margin-bottom: 0px;
  padding-top: 15px;
  font-weight: 900;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  line-height: 1.25em;
}

/* ======= Responsive Overrides ======= */
/* Mobile-specific optimizations to ensure proper scaling on small screens */

@media screen and (max-width: 767px) {
  /* Mobile-optimized slider heading sizes - LARGER for more prominent display */
  .slider-parallax-heading,
  h1.slider-parallax-heading,
  h2.slider-parallax-heading,
  h3.slider-parallax-heading,
  h4.slider-parallax-heading,
  h5.slider-parallax-heading,
  h6.slider-parallax-heading,
  div.slider-parallax-heading,
  span.slider-parallax-heading,
  p.slider-parallax-heading,
  .header .caption h1,
  .header .caption h2 {
    font-size: clamp(4.5rem, calc(4rem + 1.2vw), 5.5rem) !important; /* LARGER Mobile: 72px-88px for more prominent display */
    line-height: 1.1em !important; /* Slightly tighter for larger text */
  }

  /* Mobile-optimized slider heading spans - complete HTML tag coverage */
  .slider-parallax-heading span,
  h1.slider-parallax-heading span,
  h2.slider-parallax-heading span,
  h3.slider-parallax-heading span,
  h4.slider-parallax-heading span,
  h5.slider-parallax-heading span,
  h6.slider-parallax-heading span,
  div.slider-parallax-heading span,
  span.slider-parallax-heading span,
  p.slider-parallax-heading span,
  .header .caption h1 span,
  .header .caption h2 span {
    font-size: clamp(4.5rem, calc(4rem + 1.2vw), 5.5rem) !important; /* LARGER Mobile spans: 72px-88px for visual impact */
    line-height: 1.1em !important; /* Consistent line-height */
  }

  /* Mobile-optimized subheading - enhanced readability */
  .slider-parallax-subheading,
  h1.slider-parallax-subheading,
  h2.slider-parallax-subheading,
  h3.slider-parallax-subheading,
  h4.slider-parallax-subheading,
  h5.slider-parallax-subheading,
  h6.slider-parallax-subheading,
  div.slider-parallax-subheading,
  span.slider-parallax-subheading,
  p.slider-parallax-subheading {
    font-size: clamp(0.875rem, calc(0.8rem + 0.1vw), 1rem) !important; /* Enhanced mobile readability: 14px-16px */
    margin-bottom: 10px !important; /* Optimized spacing for mobile */
    line-height: 1.4em !important; /* Improved readability */
  }
}

/* Tablet-specific optimizations for smooth scaling between desktop and mobile */
@media screen and (min-width: 768px) and (max-width: 1199px) {
  /* Tablet-optimized slider heading sizes - smooth intermediate scaling */
  .slider-parallax-heading,
  h1.slider-parallax-heading,
  h2.slider-parallax-heading,
  h3.slider-parallax-heading,
  h4.slider-parallax-heading,
  h5.slider-parallax-heading,
  h6.slider-parallax-heading,
  div.slider-parallax-heading,
  span.slider-parallax-heading,
  p.slider-parallax-heading,
  .header .caption h1,
  .header .caption h2 {
    font-size: clamp(4.5rem, calc(4rem + 0.8vw), 5.5rem) !important; /* Tablet-optimized: 72px-88px - bridges mobile and desktop */
    line-height: 1.15em !important; /* Balanced line-height for tablet readability */
  }

  /* Tablet-optimized slider heading spans - complete HTML tag coverage */
  .slider-parallax-heading span,
  h1.slider-parallax-heading span,
  h2.slider-parallax-heading span,
  h3.slider-parallax-heading span,
  h4.slider-parallax-heading span,
  h5.slider-parallax-heading span,
  h6.slider-parallax-heading span,
  div.slider-parallax-heading span,
  span.slider-parallax-heading span,
  p.slider-parallax-heading span,
  .header .caption h1 span,
  .header .caption h2 span {
    font-size: clamp(4.5rem, calc(4rem + 0.8vw), 5.5rem) !important; /* Identical to main heading for visual consistency */
    line-height: 1.15em !important; /* Consistent line-height */
  }

  /* Tablet-optimized subheading - proportional scaling */
  .slider-parallax-subheading,
  h1.slider-parallax-subheading,
  h2.slider-parallax-subheading,
  h3.slider-parallax-subheading,
  h4.slider-parallax-subheading,
  h5.slider-parallax-subheading,
  h6.slider-parallax-subheading,
  div.slider-parallax-subheading,
  span.slider-parallax-subheading,
  p.slider-parallax-subheading {
    font-size: clamp(1rem, calc(0.9rem + 0.2vw), 1.125rem) !important; /* Tablet intermediate sizing: 16px-18px */
    line-height: 1.4em !important; /* Consistent readability */
  }
}

/* Extra small mobile devices - larger sizing for better impact */
@media screen and (max-width: 480px) {
  /* Extra small mobile optimization for slider headings - LARGER for prominence */
  .slider-parallax-heading,
  h1.slider-parallax-heading,
  h2.slider-parallax-heading,
  h3.slider-parallax-heading,
  h4.slider-parallax-heading,
  h5.slider-parallax-heading,
  h6.slider-parallax-heading,
  div.slider-parallax-heading,
  span.slider-parallax-heading,
  p.slider-parallax-heading,
  .header .caption h1,
  .header .caption h2 {
    font-size: clamp(4rem, calc(3.5rem + 1vw), 4.5rem) !important; /* LARGER Extra small mobile: 64px-72px for better impact */
    line-height: 1.1 !important; /* Consistent with main mobile sizing */
    margin-bottom: 10px !important; /* Reduced margin for space efficiency */
  }

  /* Extra small mobile spans - complete HTML tag coverage */
  .slider-parallax-heading span,
  h1.slider-parallax-heading span,
  h2.slider-parallax-heading span,
  h3.slider-parallax-heading span,
  h4.slider-parallax-heading span,
  h5.slider-parallax-heading span,
  h6.slider-parallax-heading span,
  div.slider-parallax-heading span,
  span.slider-parallax-heading span,
  p.slider-parallax-heading span,
  .header .caption h1 span,
  .header .caption h2 span {
    font-size: clamp(4rem, calc(3.5rem + 1vw), 4.5rem) !important; /* LARGER Extra small mobile spans: 64px-72px */
    line-height: 1.1 !important; /* Consistent line-height */
  }
}

/* ======= Hover Effects ======= */

/* Team Widget Hover Effects */
.team .item:hover .team-member-name,
.team .item:hover .team-member-name a,
.team .item:hover .team-member-position {
  color: #fff;
}

.team .item:hover .team-member-name {
  transform: translateX(10px);
  transition: 700ms cubic-bezier(0.17, 0.67, 0, 1.01);
}

/* About Box Hover Effects */
.about-box .item:hover .about-box-heading {
  color: #fff;
  opacity: 1;
}

/* ======= CRITICAL CSS SPECIFICITY OVERRIDES ======= */

/* Ensure all semantic classes override original selectors with maximum specificity */

/* Banner Header Override */
.banner-header h1.banner-header-heading,
.banner-header h2.banner-header-heading,
.banner-header h3.banner-header-heading,
.banner-header h4.banner-header-heading,
.banner-header h5.banner-header-heading,
.banner-header h6.banner-header-heading {
  font-size: clamp(2.25rem, calc(1.978rem + 0.085vw), 3rem) !important;
  color: #fff !important;
  position: relative !important;
  line-height: 1.25em !important;
  margin-bottom: 0 !important;
  font-weight: 900 !important;
  font-family: "Poppins", sans-serif !important;
}

/* About Item Title Override */
.about .item .wrap .title h1.about-item-title,
.about .item .wrap .title h2.about-item-title,
.about .item .wrap .title h3.about-item-title,
.about .item .wrap .title h4.about-item-title,
.about .item .wrap .title h5.about-item-title,
.about .item .wrap .title h6.about-item-title {
  color: #fff !important;
  font-size: clamp(1.25rem, calc(1.1rem + 0.2vw), 1.5rem) !important;
  margin-bottom: 5px !important;
  font-family: "Poppins", sans-serif !important;
  font-weight: 900 !important;
  line-height: 1.25em !important;
}

/* About Content Heading Override */
h1.about-content-heading,
h2.about-content-heading,
h3.about-content-heading,
h4.about-content-heading,
h5.about-content-heading,
h6.about-content-heading {
  font-size: clamp(1.25rem, calc(1.1rem + 0.2vw), 1.5rem) !important;
  color: #2c3b4c !important;
  font-family: "Poppins", sans-serif !important;
  font-weight: 900 !important;
  line-height: 1.25em !important;
  margin-bottom: 15px !important;
}

/* Team Member Name Override */
h1.team-member-name,
h2.team-member-name,
h3.team-member-name,
h4.team-member-name,
h5.team-member-name,
h6.team-member-name {
  font-size: clamp(1.125rem, calc(1rem + 0.3vw), 1.25rem) !important;
  color: #2c3b4c !important;
  line-height: 1.5em !important;
  font-family: "Poppins", sans-serif !important;
  font-weight: 900 !important;
  margin-bottom: 5px !important;
  /* text-transform: none !important; */
  letter-spacing: normal !important;
}

/* About Box Heading Override */
h1.about-box-heading,
h2.about-box-heading,
h3.about-box-heading,
h4.about-box-heading,
h5.about-box-heading,
h6.about-box-heading {
  font-size: clamp(1.25rem, calc(1.1rem + 0.2vw), 1.5rem) !important;
  color: #2c3b4c !important;
  font-family: "Poppins", sans-serif !important;
  font-weight: 900 !important;
  line-height: 1.25em !important;
  margin-bottom: 15px !important;
  /* text-transform: none !important; */
  letter-spacing: normal !important;
}

/* Blog Post Title Override */
h1.blog-post-title,
h2.blog-post-title,
h3.blog-post-title,
h4.blog-post-title,
h5.blog-post-title,
h6.blog-post-title {
  font-size: clamp(1.125rem, 2vw, 1.5rem) !important;
  margin-bottom: 15px !important;
  color: #2c3b4c !important;
  font-family: "Poppins", sans-serif !important;
  font-weight: 900 !important;
  line-height: 1.25em !important;
}

/* Blog Home Category Override */
h1.blog-home-category,
h2.blog-home-category,
h3.blog-home-category,
h4.blog-home-category,
h5.blog-home-category,
h6.blog-home-category {
  font-family: "Architects Daughter", cursive !important;
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem) !important;
  font-weight: 700 !important;
  color: #fff !important;
  display: block !important;
  margin-bottom: 10px !important;
  /* text-transform: none !important; */
  letter-spacing: normal !important;
}

/* Blog Home Title Override */
h1.blog-home-title,
h2.blog-home-title,
h3.blog-home-title,
h4.blog-home-title,
h5.blog-home-title,
h6.blog-home-title {
  color: #fff !important;
  font-size: clamp(0.95rem, 1vw + 0.4rem, 1.25rem) !important;
  margin-bottom: 15px !important;
  line-height: 1 !important;
  font-weight: 800 !important;
  font-family: "Poppins", sans-serif !important;
  /* text-transform: none !important; */
  letter-spacing: normal !important;
}

/* Blog Comment Heading Override */
h1.blog-comment-heading,
h2.blog-comment-heading,
h3.blog-comment-heading,
h4.blog-comment-heading,
h5.blog-comment-heading,
h6.blog-comment-heading {
  font-size: clamp(1.5rem, calc(1.3rem + 0.35vw), 1.625rem) !important;
  color: #2c3b4c !important;
  font-family: "Poppins", sans-serif !important;
  font-weight: 900 !important;
  line-height: 1.25em !important;
  margin-bottom: 15px !important;
  /* text-transform: none !important; */
  letter-spacing: normal !important;
}

/* Blog Sidebar Title Override */
h1.blog-sidebar-title,
h2.blog-sidebar-title,
h3.blog-sidebar-title,
h4.blog-sidebar-title,
h5.blog-sidebar-title,
h6.blog-sidebar-title {
  font-size: clamp(1.6875rem, calc(1.5rem + 0.2vw), 1.875rem) !important;
  color: #2c3b4c !important;
  font-family: "Poppins", sans-serif !important;
  font-weight: 900 !important;
  line-height: 1.25em !important;
  margin-bottom: 15px !important;
  /* text-transform: none !important; */
  letter-spacing: normal !important;
}

/* Team Single Info Label Override */
h1.team-single-info-label,
h2.team-single-info-label,
h3.team-single-info-label,
h4.team-single-info-label,
h5.team-single-info-label,
h6.team-single-info-label {
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem) !important;
  font-weight: 900 !important;
  line-height: 1.2em !important;
  white-space: normal !important;
  margin: 0 !important;
  color: #2c3b4c !important;
  font-family: "Poppins", sans-serif !important;
  /* text-transform: none !important; */
  letter-spacing: normal !important;
}

/* Team Single Info Value Override */
h1.team-single-info-value,
h2.team-single-info-value,
h3.team-single-info-value,
h4.team-single-info-value,
h5.team-single-info-value,
h6.team-single-info-value {
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem) !important;
  font-weight: 900 !important;
  white-space: normal !important;
  margin: 0 !important;
  margin-top: 0 !important;
  color: #727272 !important;
  font-family: "Hind", sans-serif !important;
  /* text-transform: none !important; */
  letter-spacing: normal !important;
}

/* Testimonial Author Name Override */
h1.testimonial-author-name,
h2.testimonial-author-name,
h3.testimonial-author-name,
h4.testimonial-author-name,
h5.testimonial-author-name,
h6.testimonial-author-name {
  display: block !important;
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.125rem) !important;
  margin-bottom: 0px !important;
  padding-top: 15px !important;
  font-weight: 900 !important;
  color: #2c3b4c !important;
  font-family: "Poppins", sans-serif !important;
  line-height: 1.25em !important;
  text-transform: none !important;
  letter-spacing: normal !important;
}

/* ======= SLIDER PARALLAX HEADING - OPTIMIZED HIERARCHICAL SEMANTIC HTML TAG SIZING ======= */

/* === Base Shared Styles for All Tags === */
.slider-parallax-heading {
  position: relative !important;
  letter-spacing: 3px !important;
  font-weight: 900 !important;
  line-height: clamp(1.2em, calc(1.1em + 0.5vw), 1.5em) !important;
  color: var(--clr-theme-color) !important;
  margin-bottom: 15px !important;
  text-transform: none !important;
  -webkit-animation-delay: 0.2s !important;
  animation-delay: 0.2s !important;
}

/* === Font Size Hierarchy === */
/* H1 - Largest: +1rem on both min and max */
h1.slider-parallax-heading {
  font-size: clamp(5rem, calc(5.5rem + 1vw), 7.875rem) !important;
  line-height: clamp(1.2em, calc(1.1em + 0.5vw), 1.5em) !important;
}

/* H2 - Existing default */
h2.slider-parallax-heading,
div.slider-parallax-heading,
span.slider-parallax-heading,
p.slider-parallax-heading {
  font-size: clamp(4rem, calc(4.5rem + 1vw), 6.875rem) !important;
}

/* H3 */
h3.slider-parallax-heading {
  /* font-size: clamp(2.5rem, calc(6vw + 1rem), 4.25rem) !important; */
  font-size: clamp(2.5rem, 6vw + 1rem, 4.25rem) !important;
  -webkit-text-stroke-width: .6px !important;
  letter-spacing: 3px !important;
  word-spacing: normal !important;
}

/* H4 */
h4.slider-parallax-heading {
  font-size: clamp(2rem, calc(5vw + 0.5rem), 3.75rem) !important;
}

/* H5 */
h5.slider-parallax-heading {
  font-size: clamp(1.5rem, calc(4vw + 0.5rem), 3.25rem) !important;
}

/* H6 */
h6.slider-parallax-heading {
  font-size: clamp(1.25rem, calc(3.5vw + 0.25rem), 2.75rem) !important;
}

/* === Span Styles: Matching Visual Hierarchy & Stroke === */
.slider-parallax-heading span {
  position: relative !important;
  color: transparent !important;
  -webkit-text-stroke-color: #d5bd95 !important;
  -webkit-text-stroke-width: clamp(0.6px, 0.15vw, 1px) !important;
  opacity: 0.8 !important;
  /* line-height: clamp(120px, calc(100px + 1vw), 150px) !important; */
  line-height: clamp(1.2em, calc(1.1em + 0.5vw), 1.5em) !important;
  -webkit-animation-delay: 0.4s !important;
  animation-delay: 0.4s !important;
}

/* H1 Span */
h1.slider-parallax-heading span {
  font-size: clamp(7.25rem, calc(6.5rem + 0.5vw), 7.5rem) !important;
  line-height: clamp(1.2em, calc(1.1em + 0.5vw), 1.5em) !important;
}

/* H2 Span */
h2.slider-parallax-heading span,
div.slider-parallax-heading span,
span.slider-parallax-heading span,
p.slider-parallax-heading span {
  font-size: clamp(6.25rem, calc(5.5rem + 0.5vw), 6.5rem) !important;
}

/* H3 Span */
h3.slider-parallax-heading span {
      /* font-size: clamp(2.5rem, 6vw + 1rem, 4.25rem) !important; */
      font-size: clamp(2.5rem, 6vw + 1rem, 4.25rem) !important;
      -webkit-text-stroke-width: .6px !important;
      letter-spacing: normal !important;
      word-spacing: normal !important;
}

/* H4 Span */
h4.slider-parallax-heading span {
  font-size: clamp(4rem, calc(3.75rem + 0.5vw), 4.75rem) !important;
}

/* H5 Span */
h5.slider-parallax-heading span {
  font-size: clamp(3.25rem, calc(3rem + 0.5vw), 4rem) !important;
}

/* H6 Span */
h6.slider-parallax-heading span {
  font-size: clamp(2.75rem, calc(2.5rem + 0.5vw), 3.5rem) !important;
}

/* Note: Slider Parallax Heading overrides moved to Critical CSS Specificity section for complete H1-H6 coverage */


/* Slider Parallax Subheading Override - Enhanced Desktop Proportion & Complete HTML Tag Coverage */
h1.slider-parallax-subheading,
h2.slider-parallax-subheading,
h3.slider-parallax-subheading,
h4.slider-parallax-subheading,
h5.slider-parallax-subheading,
h6.slider-parallax-subheading,
div.slider-parallax-subheading,
span.slider-parallax-subheading,
p.slider-parallax-subheading {
  display: block !important;
  font-weight: 400 !important;
  font-size: clamp(1rem, calc(0.875rem + 0.3vw), 1.25rem) !important; /* Enhanced desktop proportion: 16px-20px for better visual hierarchy */
  color: var(--clr-theme-color) !important;
  margin-bottom: 15px !important;
  text-transform: none !important;
  letter-spacing: 3px !important;
  line-height: clamp(1.2em, calc(1.1em + 0.5vw), 1.5em) !important;
}
