/**
 * Desktop Dropdown Menu Fix
 * Ensures parent menu links remain clickable while preserving hover dropdown functionality
 * Only affects desktop screens (≥992px) - mobile functionality preserved
 */

jQuery(document).ready(function ($) {
  "use strict";

  // Function to check if we're on desktop
  function isDesktop() {
    return window.innerWidth >= 992;
  }

  // Function to disable Bootstrap dropdown behavior for desktop
  function disableBootstrapDropdown() {
    if (isDesktop()) {
      // Remove Bootstrap dropdown attributes from parent links
      $(".navbar .dropdown-toggle").each(function () {
        var $this = $(this);

        // Store original attributes for mobile restoration
        if (!$this.data("original-toggle")) {
          $this.data("original-toggle", $this.attr("data-bs-toggle"));
          $this.data("original-auto-close", $this.attr("data-bs-auto-close"));
          $this.data("original-expanded", $this.attr("aria-expanded"));
        }

        // Remove Bootstrap dropdown attributes for desktop
        $this.removeAttr("data-bs-toggle");
        $this.removeAttr("data-bs-auto-close");
        $this.removeAttr("aria-expanded");
        $this.removeAttr("role");

        // Ensure the link is clickable
        $this.off("click.bs.dropdown");

        // Prevent Bootstrap from interfering with clicks
        $this.on("click", function (e) {
          // Allow normal link behavior (navigation)
          if ($(this).attr("href") && $(this).attr("href") !== "#") {
            return true;
          }
        });
      });

      // Hide any open Bootstrap dropdowns
      $(".navbar .dropdown-menu.show").removeClass("show");
    }
  }

  // Function to restore Bootstrap dropdown behavior for mobile
  function enableBootstrapDropdown() {
    if (!isDesktop()) {
      $(".navbar .dropdown-toggle").each(function () {
        var $this = $(this);

        // Restore original Bootstrap attributes
        if ($this.data("original-toggle")) {
          $this.attr("data-bs-toggle", $this.data("original-toggle"));
          $this.attr("data-bs-auto-close", $this.data("original-auto-close"));
          $this.attr("aria-expanded", $this.data("original-expanded"));
          $this.attr("role", "button");
        }
      });
    }
  }

  // Function to handle dropdown hover states for desktop
  function handleDesktopHover() {
    if (isDesktop()) {
      // Handle dropdown hover
      $(".navbar .dropdown").off("mouseenter.desktop mouseleave.desktop");

      $(".navbar .dropdown").on("mouseenter.desktop", function () {
        var $dropdown = $(this).find(".dropdown-menu");
        $dropdown.addClass("show-hover");
      });

      $(".navbar .dropdown").on("mouseleave.desktop", function () {
        var $dropdown = $(this).find(".dropdown-menu");
        $dropdown.removeClass("show-hover");
      });

      // Ensure dropdown stays visible when hovering over the dropdown itself
      $(".navbar .dropdown-menu").off("mouseenter.desktop mouseleave.desktop");

      $(".navbar .dropdown-menu").on("mouseenter.desktop", function () {
        $(this).addClass("show-hover");
      });

      $(".navbar .dropdown-menu").on("mouseleave.desktop", function () {
        $(this).removeClass("show-hover");
      });
    }
  }

  // Function to clean up desktop hover handlers for mobile
  function cleanupDesktopHover() {
    if (!isDesktop()) {
      $(".navbar .dropdown").off("mouseenter.desktop mouseleave.desktop");
      $(".navbar .dropdown-menu").off("mouseenter.desktop mouseleave.desktop");
      $(".navbar .dropdown-menu").removeClass("show-hover");
    }
  }

  // Initialize on page load
  function init() {
    if (isDesktop()) {
      disableBootstrapDropdown();
      handleDesktopHover();
    } else {
      enableBootstrapDropdown();
      cleanupDesktopHover();
    }
  }

  // Handle window resize
  function handleResize() {
    // Debounce resize events
    clearTimeout(window.dropdownResizeTimer);
    window.dropdownResizeTimer = setTimeout(function () {
      init();
    }, 250);
  }

  // Initialize
  init();

  // Handle window resize
  $(window).on("resize", handleResize);

  // Handle orientation change on mobile devices
  $(window).on("orientationchange", function () {
    setTimeout(init, 500);
  });

  // Ensure parent links work correctly
  $(".navbar .dropdown > .nav-link").on("click", function (e) {
    if (isDesktop()) {
      // On desktop, allow normal link behavior
      var href = $(this).attr("href");
      if (
        href &&
        href !== "#" &&
        href !== "javascript:void(0)" &&
        href !== ""
      ) {
        // Prevent any Bootstrap interference
        e.stopPropagation();
        window.location.href = href;
        return false;
      }
    }
    // On mobile, let Bootstrap handle it normally
  });

  // Additional safety: Prevent Bootstrap dropdown from interfering on desktop
  if (isDesktop()) {
    $(document).on("click", ".navbar .dropdown-toggle", function (e) {
      var href = $(this).attr("href");
      if (
        href &&
        href !== "#" &&
        href !== "javascript:void(0)" &&
        href !== ""
      ) {
        e.preventDefault();
        e.stopPropagation();
        window.location.href = href;
        return false;
      }
    });
  }
});
