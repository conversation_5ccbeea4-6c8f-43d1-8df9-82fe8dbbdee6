/*
Theme Name: TRDS Theme
Theme URI: https://trdigitalservices.com/
Description: TRDS Theme is a child theme of TR Digital Services.
Author: <PERSON><PERSON><PERSON>
Author URI: https://www.arnelbg.com
Version: 1.0
Text Domain: trds-child
Template: acens
*/

/* ========TABLE OF CONTENTS==========
00. Body, links, hgroup, paragraphs, general styles
01. Fixed header & Navigation
02. Section Intro (Home)
03. Section About
04. Section Resume
05. Section Portfolio
06. Section Blog
07. Section Contact
08. Footer
09. Loader
10. Responsive design

/*--------------------------------------------------
    00. Body, links, hgroup, paragraphs, general styles
---------------------------------------------------*/

.blog .category .post-categories {
  margin-bottom: 0px;
  padding-left: 0px;
}
.comment .comment-respond h3.comment-reply-title {
  font-size: 28px;
  color: #2c3b4c;
  line-height: 1.5em;
  font-weight: 900;
  margin-bottom: 15px;
}
body.dark .comment .comment-respond h3.comment-reply-title {
  font-size: 28px;
  color: #fff;
  line-height: 1.5em;
  font-weight: 900;
  margin-bottom: 15px;
}
p.comment-form-cookies-consent {
  display: none;
}
body.dark .comment-respond form.contact__form .form-group input::placeholder {
  color: #fff;
}
body.dark .comment-respond form.contact__form textarea::placeholder {
  color: #fff;
}
.comment .wrap a.comment-reply-link {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  line-height: 1.75em;
  -webkit-transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  border: none;
  border-radius: 80px;
  background-color: var(--clr-theme-color);
  color: #fff;
  line-height: 20px;
  text-align: center;
  padding: 10px 30px;
  height: auto;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  margin-top: 30px;
  top: 15px;
}
.comment .wrap a.comment-reply-link:hover {
  border: none;
  background: #2c3b4c;
  color: #fff;
}
body.dark .comment .wrap a.comment-reply-link:hover {
  border: none;
  background: #fff;
  color: #2c3b4c;
}
.pagination-wrap li .page-numbers {
  background: #f0f0f3;
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 46px;
  text-align: center;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  border: 2px solid #f0f0f3;
  border-radius: 50%;
  font-size: 16px;
}
.pagination-wrap li .page-numbers.current {
  background-color: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}
body.dark .pagination-wrap li a {
  background: #2c3b4c;
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 46px;
  text-align: center;
  color: #fff;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  border: 2px solid #2c3b4c;
  border-radius: 50%;
  font-size: 16px;
}
body.dark .pagination-wrap li .page-numbers.current {
  background-color: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}
.blog2 .item .wrapper .con .category i {
  transition: all ease 0.4s;
  color: var(--clr-theme-color);
  margin-right: 5px;
  font-size: 12px;
}
.blog2 .item .wrapper .con .category a {
  text-transform: capitalize;
}
.blog2 .item:hover .wrapper .con .category i {
  color: #fff;
}
.blog2 .item .wrapper .con .category .post-categories {
  display: inline-block;
  padding-left: 0px;
}
.widget .tagcloud .wp-tag-cloud li a {
  font-size: 14px !important;
  color: #727272;
  margin: 5px !important;
  padding: 5px 25px;
  background-color: transparent;
  border: none;
  float: left;
  border: 2px solid var(--clr-theme-color);
  border-radius: 30px;
  line-height: 1.5em;
}
.widget .tagcloud .wp-tag-cloud li a:hover {
  background-color: var(--clr-theme-color);
  color: #fff;
}
body.dark .widget .tagcloud .wp-tag-cloud li a:hover {
  background-color: var(--clr-theme-color);
  color: #fff;
}
body.dark .event-detail .item .con h5 {
  font-size: 24px;
  line-height: 1.5em;
  font-weight: 900;
  margin-bottom: 15px;
  color: #fff;
}
.navbar .dropdown-menu .nav-link {
  padding: 7px 0;
  font-size: 16px;
  color: #2c3b4c;
  position: relative;
  background-color: transparent;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  font-weight: 400;
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  font-family: "Hind", sans-serif;
}
.navbar .dropdown-menu .nav-link:hover {
  color: var(--clr-theme-color);
}
body.dark .navbar .dropdown-menu .nav-link {
  display: block;
  width: 100%;
  clear: both;
  font-weight: 400;
  color: var(--bs-dropdown-link-color);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  border: 0;
  padding: 7px 0;
  font-size: 16px;
  color: #fff;
  position: relative;
  background-color: transparent;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  font-family: "Hind", sans-serif;
}
body.dark .navbar .dropdown-menu .nav-link:hover {
  color: var(--clr-theme-color);
}
/* ============================
   Mobile Dropdown Menu Styling (≤991px)
   Note: Mobile styling preserved as-is
   ============================ */
@media screen and (max-width: 991px) {
  .navbar .dropdown-menu .nav-link {
    color: #2c3b4c;
    padding: 15px 15px;
    font-size: 16px;
    line-height: 40px;
  }
  body.dark .navbar .dropdown-menu .nav-link {
    color: #fff;
    padding: 0 15px;
    font-size: 16px;
    line-height: 40px;
  }
  .navbar .dropdown-menu .dropdown-menu {
    margin: 0;
    padding: 0 0 0 20px !important;
  }
}
.search .search-custom {
  padding: 50px;
  background-color: #f0f0f3;
  border-radius: 10px;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}
.search .search-custom h4 {
  margin-bottom: 20px;
  font-size: 18px;
  line-height: 28px;
  letter-spacing: 1px;
  color: #2c3b4c;
}
.search .search-custom form {
  display: flex;
  position: relative;
  width: 70%;
}
.search .search-custom form .s-input-home {
  height: 60px;
  padding-left: 10px;
  max-width: 100%;
  border: 1px solid #fff;
  padding: 0px 10px 0px;
  margin-bottom: 0px;
}
.search .search-custom form button {
  position: absolute;
  right: 5px;
  top: 5px;
  background-color: var(--clr-theme-color);
  color: #fff;
  border: 0;
  padding: 16px 17px 14px 17px;
  cursor: pointer;
  border-radius: 100%;
}
.search .search-custom form .btn-s-input .fa-search {
  font-weight: 900;
}
.services .item.active .wrap .con h4 a {
  color: #fff;
}

.services .item:hover .wrap .con h4 a {
  color: #fff;
}

.services .item.active .wrap .con h3 a {
  color: #fff;
}

.services .item:hover .wrap .con h3 a {
  color: #fff;
}

.services .item.active .wrap .con h2 a {
  color: #fff;
}

.services .item:hover .wrap .con h2 a {
  color: #fff;
}

.sidebar-widget.services .widget-inner .navbar-nav .nav-link {
  padding: 0px;
}
.contact form .contact__form textarea {
  height: 130px;
}
.contact form .contact__form .wpcf7-not-valid-tip {
  color: #2c3b4c;
  font-size: 1em;
  font-weight: normal;
  display: block;
}
.contact .wpcf7 form.invalid .wpcf7-response-output {
  border-color: #2c3b4c;
  color: #2c3b4c;
  margin: 2em 0em 1em;
}
body.dark .contact form .contact__form .wpcf7-not-valid-tip {
  color: #fff;
  font-size: 1em;
  font-weight: normal;
  display: block;
}
body.dark .contact .wpcf7 form.invalid .wpcf7-response-output {
  border-color: #fff;
  color: #fff;
  margin: 2em 0em 1em;
}
body.dark .contact form .contact__form input::placeholder {
  color: #fff;
}
body.dark .contact form .contact__form textarea::placeholder {
  color: #fff;
}
.subscribe form input.wpcf7-form-control.has-spinner.wpcf7-submit {
  white-space: break-spaces;
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  background-color: #fff;
  padding: 16px 17px 14px 17px;
  border-radius: 100%;
  color: var(--clr-theme-color);
  border: 0;
  position: absolute;
  top: 3.25px;
  right: 6px;
  cursor: pointer;
  outline: none;
  transform: rotate(0);
  width: auto;
}
.subscribe form .wpcf7-not-valid-tip {
  color: #fff;
  font-size: 1em;
  font-weight: normal;
  display: block;
}
.subscribe form .wpcf7-response-output {
  border-color: #fff;
  margin: 0em 0em 1em;
  color: #fff;
}
#map iframe {
  margin-bottom: -10px;
}
.works .item .wrap {
  cursor: auto;
}
.video-popup .vid-butn .vid .icon {
  color: var(--clr-theme-color);
  width: 100px;
  height: 100px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  line-height: 110px;
  text-align: center;
  font-size: 40px;
  position: relative;
  -webkit-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  display: inline-block;
}
.video-popup .vid-butn .vid:hover .icon {
  color: #fff;
  transform: translate3d(0px, -5px, 0.01px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d;
}
.video-popup .vid-butn .vid .icon:before {
  content: "";
  position: absolute;
  top: 5px;
  bottom: 5px;
  right: 5px;
  left: 5px;
  background-color: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  border-radius: 50%;
  z-index: -1;
  -webkit-transition: all 0.5s cubic-bezier(1, 0, 0, 1);
  -o-transition: all 0.5s cubic-bezier(1, 0, 0, 1);
  transition: all 0.5s cubic-bezier(1, 0, 0, 1);
}
.video-popup .vid-butn .vid .icon:after {
  content: "";
  position: absolute;
  top: 5px;
  bottom: 5px;
  right: 5px;
  left: 5px;
  border-radius: 50%;
  z-index: -1;
}
.video-popup .vid-butn .vid:hover .icon:before {
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
}
body.dark .works .item .wrap {
  cursor: auto;
}
.clients .clients-logo a {
  cursor: auto;
}
.footer .subscribe form .wpcf7-response-output {
  border-color: #fff;
}
body.dark .blog-home .item .wrap {
  cursor: auto;
}
.blog-home .item .wrap {
  cursor: auto;
}
.about .item .wrap {
  cursor: auto;
}
body.dark .about .item .wrap {
  cursor: auto;
}
.widget .tagcloud .wp-tag-cloud li a {
  text-transform: capitalize;
}
.comment-respond .comment-reply-title small > a {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  line-height: 1.75em;
  -webkit-transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  border: none;
  border-radius: 80px;
  background-color: var(--clr-theme-color);
  color: #fff;
  line-height: 20px;
  text-align: center;
  padding: 7px 24px;
  height: auto;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  float: right;
  font-size: 15px;
}
.comment-respond .comment-reply-title small > a:hover {
  border: none;
  background: #2c3b4c;
  color: #fff;
}
body.dark .comment-respond .comment-reply-title small > a:hover {
  border: none;
  background: #fff;
  color: #2c3b4c;
}
body.dark .section-padding[data-overlay-dark] h6,
body.dark .section-padding h6 {
  font-weight: 600;
}
body.dark .events .item .text p {
  font-weight: 600;
}
body.dark .blog-home .item .wrap .title h6 a {
  font-weight: 600;
}
body.dark .navbar .navbar-right .wrap .text p {
  font-weight: 600;
}
body.dark .works .item .wrap .text p {
  font-weight: 600;
}
body.dark .gallery-box .gallery-detail p {
  font-weight: 600;
}
body.dark .video-gallery .item .text p {
  font-weight: 600;
}
body.dark .event-detail .item .con a {
  font-weight: 600;
}
.error404 .lets-talk[data-overlay-dark] h6,
.error404 .lets-talk h6 {
  font-weight: 600;
}
body.dark .subscribe form input.wpcf7-form-control.has-spinner.wpcf7-submit {
  top: 4.75px;
  right: 6px;
}
.subscribe form input.wpcf7-form-control.has-spinner.wpcf7-submit {
  top: 4.75px;
  right: 6px;
}
body.dark .events .btn-3:hover {
  border: 2px solid #fff;
  background-color: #fff;
  color: #2c3b4c;
}
.nav-scroll .navbar-right .wrap .text h5,
.nav-scroll .navbar-right .wrap .text h5 a:hover {
  color: #2c3b4c;
}
.single-event .team-single.section-padding {
  background: #f0f0f3;
}
body.dark.single-event .team-single.section-padding {
  background: #2c3b4c;
}
.blog.section-padding .page-numbers {
  background: #f0f0f3;
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 46px;
  text-align: center;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  border: 2px solid #f0f0f3;
  border-radius: 50%;
  font-size: 16px;
}
.blog.section-padding .page-numbers:hover {
  opacity: 1;
  text-decoration: none;
  background: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}
.blog.section-padding .page-numbers.current {
  background-color: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}
.blog2 .page-numbers {
  background: #f0f0f3;
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 46px;
  text-align: center;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  border: 2px solid #f0f0f3;
  border-radius: 50%;
  font-size: 16px;
  margin-right: 10px;
}
.blog2 .page-numbers:hover {
  opacity: 1;
  text-decoration: none;
  background: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}
.blog2 .page-numbers.current {
  background-color: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}

.blog-home .page-numbers {
  background: #f0f0f3;
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 46px;
  text-align: center;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  border: 2px solid #f0f0f3;
  border-radius: 50%;
  font-size: 16px;
  margin-right: 10px;
}
.blog-home .page-numbers:hover {
  opacity: 1;
  text-decoration: none;
  background: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}
.blog-home .page-numbers.current {
  background-color: var(--clr-theme-color);
  border: 2px solid var(--clr-theme-color);
  color: #fff;
}
.blog .category .post-categories {
  margin-bottom: 0px;
  padding-left: 0px;
}
.comment .comment-respond h3.comment-reply-title {
  font-size: 28px;
  color: #2c3b4c;
  line-height: 1.5em;
  font-weight: 900;
  margin-bottom: 15px;
}
body.dark .comment .comment-respond h3.comment-reply-title {
  font-size: 28px;
  color: #fff;
  line-height: 1.5em;
  font-weight: 900;
  margin-bottom: 15px;
}
p.comment-form-cookies-consent {
  display: none;
}
body.dark .comment-respond form.contact__form .form-group input::placeholder {
  color: #fff;
}
body.dark .comment-respond form.contact__form textarea::placeholder {
  color: #fff;
}
.comment .wrap a.comment-reply-link {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  line-height: 1.75em;
  -webkit-transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  border: none;
  border-radius: 80px;
  background-color: #dd9933;
  color: #fff;
  line-height: 20px;
  text-align: center;
  padding: 10px 30px;
  height: auto;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  margin-top: 0px;
  position: absolute;
  top: 0px;
  right: 0;
}
.comment .wrap a.comment-reply-link:hover {
  border: none;
  background: #2c3b4c;
  color: #fff;
}
body.dark .comment .wrap a.comment-reply-link:hover {
  border: none;
  background: #fff;
  color: #2c3b4c;
}
.pagination-wrap li .page-numbers {
  background: #f0f0f3;
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 46px;
  text-align: center;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  border: 2px solid #f0f0f3;
  border-radius: 50%;
  font-size: 16px;
}
.pagination-wrap li .page-numbers.current {
  background-color: #dd9933;
  border: 2px solid #dd9933;
  color: #fff;
}
body.dark .pagination-wrap li a {
  background: #2c3b4c;
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 46px;
  text-align: center;
  color: #fff;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  border: 2px solid #2c3b4c;
  border-radius: 50%;
  font-size: 16px;
}
body.dark .pagination-wrap li .page-numbers.current {
  background-color: #dd9933;
  border: 2px solid #dd9933;
  color: #fff;
}
.blog2 .item .wrapper .con .category i {
  transition: all ease 0.4s;
  color: #dd9933;
  margin-right: 5px;
  font-size: 12px;
}
.blog2 .item .wrapper .con .category a {
  text-transform: capitalize;
}
.blog2 .item:hover .wrapper .con .category i {
  color: #fff;
}
.blog2 .item .wrapper .con .category .post-categories {
  display: inline-block;
  padding-left: 0px;
}
.widget .tagcloud .wp-tag-cloud li a {
  font-size: 14px !important;
  color: #2c3b4c;
  margin: 5px !important;
  padding: 5px 25px;
  background-color: transparent;
  border: none;
  float: left;
  border: 2px solid #dd9933;
  border-radius: 30px;
  line-height: 1.5em;
  display: block !important;
}
.widget .tagcloud .wp-tag-cloud li a:hover {
  background-color: #dd9933;
  color: #fff;
}
body.dark .widget .tagcloud .wp-tag-cloud li a:hover {
  background-color: #dd9933;
  color: #fff;
}
body.dark .event-detail .item .con h5 {
  font-size: 24px;
  line-height: 1.5em;
  font-weight: 900;
  margin-bottom: 15px;
  color: #fff;
}
.navbar .dropdown-menu .nav-link {
  padding: 7px 0;
  font-size: 16px;
  color: #2c3b4c;
  position: relative;
  background-color: transparent;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  font-weight: 400;
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  font-family: "Hind", sans-serif;
}
.navbar .dropdown-menu .nav-link:hover {
  color: #dd9933;
}
body.dark .navbar .dropdown-menu .nav-link {
  display: block;
  width: 100%;
  clear: both;
  font-weight: 400;
  color: var(--bs-dropdown-link-color);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  border: 0;
  padding: 7px 0;
  font-size: 16px;
  color: #fff;
  position: relative;
  background-color: transparent;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  font-family: "Hind", sans-serif;
}
body.dark .navbar .dropdown-menu .nav-link:hover {
  color: #dd9933;
}
@media screen and (max-width: 991px) {
  .navbar .dropdown-menu .nav-link {
    color: #2c3b4c;
    padding: 0 15px;
    font-size: 16px;
    line-height: 40px;
  }
  body.dark .navbar .dropdown-menu .nav-link {
    color: #fff;
    padding: 0 15px;
    font-size: 16px;
    line-height: 40px;
  }
  .navbar .dropdown-menu .dropdown-menu {
    margin: 0;
    padding: 0 0 0 20px !important;
  }
}
.search .search-custom {
  padding: 50px;
  background-color: #f0f0f3;
  border-radius: 10px;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}
.search .search-custom h4 {
  margin-bottom: 20px;
  font-size: 18px;
  line-height: 28px;
  letter-spacing: 1px;
  color: #2c3b4c;
}
.search .search-custom form {
  display: flex;
  position: relative;
  width: 70%;
}
.search .search-custom form .s-input-home {
  height: 60px;
  padding-left: 10px;
  max-width: 100%;
  border: 1px solid #fff;
  padding: 0px 10px 0px;
  margin-bottom: 0px;
}
.search .search-custom form button {
  position: absolute;
  right: 0px;
  top: 0px;
  background-color: #dd9933;
  color: #fff;
  border: 0;
  padding: 16px 17px 14px 17px;
  cursor: pointer;
  border-radius: 5px;
}
.search .search-custom form .btn-s-input .fa-search {
  font-weight: 900;
}
.services .item.active .wrap .con h4 a {
  color: #fff;
}
.services .item:hover .wrap .con h4 a {
  color: #fff;
}
.sidebar-widget.services .widget-inner .navbar-nav .nav-link {
  padding: 0px;
}
.contact form .contact__form textarea {
  height: 130px;
}
.contact form .contact__form .wpcf7-not-valid-tip {
  color: #2c3b4c;
  font-size: 1em;
  font-weight: normal;
  display: block;
}
.contact .wpcf7 form.invalid .wpcf7-response-output {
  border-color: #2c3b4c;
  color: #2c3b4c;
  margin: 2em 0em 1em;
}
body.dark .contact form .contact__form .wpcf7-not-valid-tip {
  color: #fff;
  font-size: 1em;
  font-weight: normal;
  display: block;
}
body.dark .contact .wpcf7 form.invalid .wpcf7-response-output {
  border-color: #fff;
  color: #fff;
  margin: 2em 0em 1em;
}
body.dark .contact form .contact__form input::placeholder {
  color: #fff;
}
body.dark .contact form .contact__form textarea::placeholder {
  color: #fff;
}
.subscribe form input.wpcf7-form-control.has-spinner.wpcf7-submit {
  white-space: break-spaces;
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  background-color: #fff;
  padding: 16px 17px 14px 17px;
  border-radius: 100%;
  color: #dd9933;
  border: 0;
  position: absolute;
  top: 4.75px;
  right: 6px;
  cursor: pointer;
  outline: none;
  transform: rotate(0);
  width: auto;
  height: auto;
}
.subscribe form .wpcf7-not-valid-tip {
  color: #fff;
  font-size: 1em;
  font-weight: normal;
  display: block;
}
.subscribe form .wpcf7-response-output {
  border-color: #fff;
  margin: 0em 0em 1em;
  color: #fff;
}
.gallery-caption {
  color: #2c3b4c;
}
.bypostauthor {
  color: #2c3b4c;
}
body {
  color: #2c3b4c;
  line-height: 28px;
}
p {
  font-size: 15px !important;
  font-weight: 400;
  line-height: 28px;
  font-family: "Open Sans", sans-serif;
  color: #2c3b4c;
  margin-bottom: 10px;
}
.widget li {
  line-height: 40px;
  color: #2c3b4c;
}
.widget a {
  font-weight: 400;
  font-size: 16px;
  float: left;
  color: #2c3b4c;
  display: contents !important;
}
.widget a:hover {
  color: #dd9933;
}
.widget .widget-title-box.mb-30 {
  margin-bottom: 20px;
}
.widget select {
  width: 100%;
  padding: 10px 15px;
  border: solid 1px #2c3b4c;
  color: #2c3b4c;
  margin-top: 0px;
}
.widget_calendar table {
  border: 1px solid #2c3b4c !important;
  padding: 10px !important;
  color: #2c3b4c !important;
  border-collapse: collapse !important;
  width: 100%;
  border-bottom: 0px !important;
}
.widget_calendar table * {
  border: 1px solid #2c3b4c !important;
  padding: 5px !important;
  color: #2c3b4c !important;
}
.widget_calendar .calendar_wrap * {
  text-align: center !important;
}
.widget_calendar caption {
  caption-side: top;
  border-bottom: none !important;
  text-transform: uppercase;
  font-weight: 500;
  background: #dd9933 !important;
  color: #fff !important;
}
.widget_calendar tfoot {
  display: none;
}
.widget_calendar td#today {
  background: #dd9933 !important;
  color: #fff !important;
}
.widget_calendar .calendar_wrap thead tr th {
  background: #dd9933 !important;
  color: #fff !important;
}
.widget > ul > li:last-child {
  margin-bottom: -10px !important;
}
ul.children {
  padding-left: 20px;
  clear: both;
}
.widget ul.sub-menu {
  padding-left: 15px;
  clear: both;
}
.widget_rss .sidebar-title a {
  font-weight: 600;
  position: relative;
  color: #2c3b4c;
  font-size: 22px;
  line-height: 1.3em;
}
.widget a.rsswidget {
  padding-bottom: 5px;
  border-bottom: 2px solid rgba(0, 0, 0, 0.04);
  font-size: 24px;
  line-height: 1.75em;
  margin-bottom: 15px;
  color: #18191d;
  font-weight: 700;
}
.widget a.rsswidget img {
  width: auto;
}
.widget .rss-date {
  font-style: italic;
  font-size: 13px !important;
  color: #2c3b4c;
  clear: both;
  display: block;
  font-weight: 700;
}
.blog-sidebar .widget .widget_rss > ul > li {
  border-bottom: 1px dashed #cccccc;
  margin-bottom: 10px;
  padding-bottom: 10px;
}
.widget_rss cite {
  font-weight: 700;
  text-transform: capitalize;
  font-size: 12px;
}
.widget_rss > ul > li:last-child {
  border: none;
  margin-bottom: 0px;
  padding-bottom: 0px;
}
.widget_search > form {
  position: relative;
}
.widget_search > form > input {
  width: 100%;
  height: 60px;
  border: 1px solid #eee;
  background: #fff;
  padding: 0 20px;
}
.widget_search > form .btn-s-input {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  background: #2c3b4c;
  padding: 0 25px;
  color: #ffffff;
  line-height: 60px;
  border: 1px solid #2c3b4c;
}
img {
  display: inline-block;
  max-width: 100%;
  height: auto;
  max-height: 100%;
}
.widget_text .textwidget > p strong {
  display: inline;
  margin-bottom: 0 !important;
  margin-top: 0 !important;
  color: #2c3b4c;
  font-weight: 700;
}
.widget_text .textwidget > p strong:first-child {
  margin-top: 0px !important;
  color: #2c3b4c;
}

/* Ensure strong tags remain inline by default */
strong {
  display: inline !important;
  font-weight: 700;
}
.widget_text br {
  display: none;
}
.widget_tag_cloud.widget {
  display: flow-root;
}
.widget_tag_cloud .wp-tag-cloud a:hover {
  background: #2c3b4c;
  color: #ffffff;
}
.copyright-text p {
  color: #ffff;
}
.read-more.mt-30 {
  margin-top: 20px;
}
.post-meta {
  margin-bottom: 18px;
}
.blog-title {
  margin-bottom: 15px;
}
.breadcrumb-area {
  background: #002bb7 !important;
}
.footer-default {
  background-color: #002bb7;
}
.featured-post {
  display: block;
  color: white;
  padding: 3px 20px 3px 20px;
  font-size: 14px;
  font-weight: 600;
  float: right;
  margin-right: 2px;
  border-radius: 2px;
  letter-spacing: 1px;
  margin-right: 40px;
  margin: 0px !important;
  background-color: #1a1a1a;
  border: none;
  margin-bottom: 20px;
  font-size: 16px;
}
.blog-coment-title h2 {
  font-size: 28px;
}
.wp-block-image {
  margin-block-start: 1em;
}
.wp-block-image figcaption {
  margin-top: 10px;
  margin-bottom: 0em;
  color: #2c3b4c;
  text-align: center;
  font-size: 15px;
  font-weight: 600;
}
.wp-block-image figcaption a {
  color: #2c3b4c;
}
.wp-block-cover.has-background-dim-60.has-background-dim:last-child {
  margin-bottom: 0px !important;
}
.postid-1 .comments-box {
  padding-bottom: 0px;
}
.wp-block-gallery.columns-3 .blocks-gallery-image,
.wp-block-gallery.columns-3 .blocks-gallery-item {
  margin-right: 0px;
}
@media (min-width: 600px) {
  .wp-block-gallery.columns-3 .blocks-gallery-image,
  .wp-block-gallery.columns-3 .blocks-gallery-item {
    width: auto;
  }
}
.wp-block-gallery .blocks-gallery-image,
.wp-block-gallery .blocks-gallery-item {
  width: auto;
}
@media (min-width: 600px) {
  .wp-block-gallery.columns-4 .blocks-gallery-image,
  .wp-block-gallery.columns-4 .blocks-gallery-item {
    width: auto;
    margin-right: 16px;
  }
}
.wp-block-gallery .blocks-gallery-image figure,
.wp-block-gallery .blocks-gallery-item figure {
  display: block;
}
.wp-block-gallery .blocks-gallery-image,
.wp-block-gallery .blocks-gallery-item {
  width: auto;
}
@media (min-width: 600px) {
  .wp-block-gallery.columns-5 .blocks-gallery-image,
  .wp-block-gallery.columns-5 .blocks-gallery-item {
    width: auto;
    margin-right: 16px;
    max-width: 100%;
  }
  .wp-block-gallery.columns-3 .blocks-gallery-image,
  .wp-block-gallery.columns-3 .blocks-gallery-item {
    width: auto;
  }
  .wp-block-gallery.columns-6 .blocks-gallery-image,
  .wp-block-gallery.columns-6 .blocks-gallery-item {
    width: auto;
    margin-right: 16px;
  }
  .wp-block-gallery.columns-4 .blocks-gallery-image,
  .wp-block-gallery.columns-4 .blocks-gallery-item {
    width: auto;
    margin-right: 16px;
  }
  .wp-block-gallery.columns-7 .blocks-gallery-image,
  .wp-block-gallery.columns-7 .blocks-gallery-item {
    width: auto;
    margin-right: 16px;
  }
  .wp-block-gallery.columns-8 .blocks-gallery-image,
  .wp-block-gallery.columns-8 .blocks-gallery-item {
    width: auto;
    margin-right: 16px;
  }
  .wp-block-gallery.columns-8 li.blocks-gallery-item:first-child {
    max-width: 640px;
  }
}
.wp-block-gallery .blocks-gallery-image figure,
.wp-block-gallery .blocks-gallery-item figure {
  display: block;
}
.blog-single ul.wp-block-gallery {
  padding-left: 0px;
}
.wp-block-gallery.columns-3 .blocks-gallery-image,
.wp-block-gallery.columns-3 .blocks-gallery-item {
  margin-right: 0px;
}
.wp-block-gallery.columns-3 .blocks-gallery-image,
.wp-block-gallery.columns-3 .blocks-gallery-item img {
  width: 100% !important;
}
.wp-block-gallery.is-cropped .blocks-gallery-item img.wp-image-1692 {
  width: auto;
}
.wp-block-gallery {
  display: block;
  flex-wrap: wrap;
  list-style-type: none;
  padding: 0;
}
.wp-block-gallery.is-cropped .blocks-gallery-image a,
.wp-block-gallery.is-cropped .blocks-gallery-image img,
.wp-block-gallery.is-cropped .blocks-gallery-item a,
.wp-block-gallery.is-cropped .blocks-gallery-item img {
  width: auto;
}
ul.wp-block-gallery.columns-4 > .blocks-gallery-item:last-child {
  max-width: 640px !important;
}
ul.wp-block-gallery.columns-6 > .blocks-gallery-item:last-child {
  max-width: 640px !important;
}
.wp-block-media-text .wp-block-media-text__media {
  display: contents;
}
.wp-block-pullquote:not(.is-style-solid-color) {
  background: none;
  padding: 0px 0;
  margin-bottom: 0px;
  margin: 0 0 0rem;
}
.has-cyan-bluish-gray-background-color.has-cyan-bluish-gray-background-color {
  margin-bottom: 0px;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #2c3b4c;
}
.section-padding h1 {
  font-size: clamp(2rem, 2.625vw + 1rem, 2.625rem);
  font-weight: 900;
  margin-bottom: 1rem;
}
.section-padding h1 + h2 {
  font-size: clamp(1.75rem, 2.25vw + 0.5rem, 2.25rem) !important;
  margin-bottom: 1rem;
}
.section-padding h1 + h2 + h3 {
  margin-bottom: 1rem;
  font-size: clamp(1.5rem, 2vw + 0.5rem, 2rem) !important;
}
.section-padding h1 + h2 + h3 + h4 {
  margin-bottom: 1rem;
  font-size: clamp(1.25rem, 1.75vw + 0.5rem, 1.75rem);
}
.section-padding h1 + h2 + h3 + h4 + h5 {
  margin-bottom: 1rem;
  font-size: clamp(1.125rem, 1.5vw + 0.5rem, 1.5rem);
  color: #2c3b4c;
}
.section-padding h1 + h2 + h3 + h4 + h5 + h6 {
  margin-bottom: 1rem;
}
.section-padding h5 + h6 {
  color: #2c3b4c;
  text-transform: capitalize;
  font-size: 20px;
  font-weight: 900;
  font-family: "Poppins", sans-serif;
}
.section-padding h2 {
  font-size: 36px;
  line-height: 1.25em;
  font-weight: 900;
  margin-bottom: 5px;
  color: #2c3b4c;
  font-family: "Poppins", sans-serif;
}
ol li a {
  color: #2c3b4c;
}
ul li a {
  color: #2c3b4c;
}
.wp-block-latest-posts__post-date {
  display: block;
  color: #2c3b4c;
  font-size: 13px;
}
pre {
  display: block;
  padding: 20px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.42857143;
  color: #2c3b4c;
  word-break: break-all;
  word-wrap: break-word;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-top: 15px;
  margin-bottom: 15px;
}
pre.wp-block-verse:last-child {
  margin-bottom: 0px;
}
dl,
ol,
ul {
  color: #2c3b4c;
}
address {
  color: #2c3b4c;
}
code {
  color: #2c3b4c;
}
div.page-links {
  color: #2c3b4c;
  clear: both;
  margin-top: 20px;
  display: block;
  float: left;
}
.post-password-form #pwbox-1168 {
  height: 42px;
}
.comments-text p:last-child {
  margin-bottom: -5px;
}
.comments-text {
  padding-top: 0px;
}
.comments-box.no-avatar .comments-text {
  margin-left: 0px;
}
.avatar-name h5 {
  font-size: 18px;
  margin-bottom: 5px;
  margin-top: -3px;
  text-transform: capitalize;
}
.avatar-name {
  margin-bottom: 10px;
}
.avatar-name span {
  color: #2c3b4c;
  font-size: 12px;
  font-weight: 400;
  text-transform: none;
  letter-spacing: 2px;
}
.avatar-name span {
  color: #2c3b4c;
  font-size: 12px;
  font-weight: 400;
  text-transform: none;
  letter-spacing: 1px;
}
.post-meta span {
  color: #2c3b4c;
}
.post-meta span a {
  color: #2c3b4c;
}
.comments-box.no-avatar {
  min-height: auto;
}
.wp-block-image .aligncenter > figcaption,
.wp-block-image .alignleft > figcaption,
.wp-block-image .alignright > figcaption,
.wp-block-image.is-resized > figcaption {
  display: block;
}
.main-menu ul li:hover > ul.submenu ul.submenu {
  left: 231px;
  top: -18px;
}
label {
  color: #2c3b4c;
}
.comments-text {
  overflow: inherit;
  margin-left: 130px;
  line-height: 28px;
}
.reply {
  position: inherit;
}
@media (max-width: 1024px) {
  #respond.comment-respond .conatct-post-form p.form-submit .submit {
    width: 43%;
  }
  .post-meta span {
    margin-right: 13px;
  }
}
@media (max-width: 991px) {
  .widget_calendar table * {
    padding: 2px !important;
  }
}
@media (max-width: 767px) {
  /* Fix spacing between button and images in About Other Pages widget */
  .about .btn-3.mt-15 {
    margin-bottom: 30px;
  }

  /* Additional spacing for very small mobile devices */
  .about .item {
    margin-top: 20px;
  }

  .featured-post {
    float: left;
    clear: both;
    margin-bottom: 20px !important;
    margin-top: 10px;
  }
  .blog-post-categorydate-wrapper {
    clear: both;
    display: block;
  }
  .blog-post-categorydate-divider {
    display: none;
  }
  .blog-title {
    font-size: 26px;
  }
  .widget_calendar table * {
    padding: 7px !important;
  }
  .conatct-post-form textarea {
    padding: 18px;
  }
  #respond.comment-respond .conatct-post-form p.form-submit .submit {
    width: 100%;
  }
  .wp-block-gallery .blocks-gallery-item figcaption {
    line-height: 16px;
  }
  .fb_iframe_widget iframe {
    max-width: 100%;
  }
  .fb_iframe_widget span {
    max-width: 100%;
  }
  .fb_iframe_widget {
    max-width: 100%;
  }
  .wp-block-embed {
    max-width: 100%;
  }
  .post-meta span {
    float: left;
  }
  .post-meta span {
    margin-right: 10px;
  }
  .post-meta span {
    font-size: 13px;
    letter-spacing: 1px;
    font-weight: 500;
    text-transform: capitalize;
  }
  .post-password-form p + p {
    display: flex;
  }
  .post-password-form p + p #pwbox-1168 {
    width: 80%;
  }
  .post-password-form p label[for="pwbox-1168"] {
    display: flex;
  }
  .comments-text {
    margin-top: 20px;
  }
  .comments-text {
    margin-left: 0px;
  }
}
@media (max-width: 600px) {
  .featured-post {
    float: left;
    clear: both;
    margin-bottom: 20px !important;
    margin-top: 10px;
  }
}
.search .search-custom {
  background: #fff;
  padding: 30px;
}
.search .search-custom {
  position: relative;
  background: #f0f0f3;
  padding: 45px 30px;
  margin-bottom: 30px;
  display: grid;
  border-radius: 10px;
}
.search .search-custom h4 {
  margin-bottom: 20px;
  font-size: 18px;
  line-height: 28px;
  letter-spacing: 1px;
}
.search .search-custom form {
  display: flex;
}
.search .search-custom form .s-input-home {
  width: 70%;
  height: 42px;
  padding-left: 10px;
  max-width: 100%;
  border: 1px solid #999;
}
.search .search-custom form .btn-s-input {
  background: #2c3b4c;
  color: #fff;
  border: 1px solid #2c3b4c;
  padding: 6px 20px;
}
.widget .recentcomments a {
  display: contents !important;
  font-weight: 500;
}
.widget .recentcomments {
  border-bottom: 1px dashed #cccccc;
  margin-bottom: 10px;
  padding-bottom: 10px;
  line-height: 28px;
}
.widget .recentcomments:last-child {
  border-bottom: 0px dashed #cccccc;
  margin-bottom: 0px;
  padding-bottom: 0px;
}
.widget .recentcomments a:hover {
  color: #2c3b4c;
}
.widget h4 a.rsswidget {
  font-size: 22px;
}
.has-very-light-gray-color.has-very-light-gray-color {
  color: #fff;
}
#wp-block-archives-5e578f55eb51f {
  width: 100%;
  padding: 10px 15px;
  border: solid 1px #2c3b4c;
  color: #2c3b4c;
  margin-bottom: 10px;
}
.main-menu ul li ul.submenu li a {
  color: #2c3b4c;
  line-height: 28px;
}

.mean-container .mean-nav ul li li a {
  opacity: 1;
}
#recent-comments-2.widget .widget-title-box.mb-30 {
  margin-bottom: 25px;
}
.wp-block-image.size-full.wp-image-906 {
  margin-bottom: 5px;
}
.wp-block-pullquote.is-style-solid-color blockquote p {
  margin-bottom: 10px;
}
.wp-block-archives .screen-reader-text + select {
  width: 100%;
  padding: 10px 15px;
  border: solid 1px #2c3b4c;
  color: #2c3b4c;
  margin-bottom: 10px;
}
.wp-block-media-text.has-media-on-the-right {
  margin-bottom: 20px;
}
.news-sidebar-widget .widget-categories ul li,
.news-sidebar-widget .widget-archives ul li {
  display: block;
  padding: 0px 0;
  border-bottom: 0px dashed #ccc;
}
.widget li {
  display: block;
}
.news-description {
  padding: 30px;
  background: #f9f9f9;
}
.blog-sidebar .widget ul li.recentcomments {
  border-bottom: 1px dashed #cccccc;
  margin-bottom: 10px;
  padding-bottom: 10px;
  line-height: 28px;
}
.widget-wrap ul li.cat-item::before {
  display: none !important;
}
.wp-block-gallery.alignleft,
.wp-block-gallery.alignright {
  max-width: 100%;
}
.wp-block-cover .wp-block-cover-text {
  color: #fff;
  z-index: 1;
  max-width: 610px;
  padding: 14px;
  text-align: center;
}

.wp-block-button.alignright {
  float: right;
  margin-left: 20px !important;
  margin-top: 9px;
}
.wp-block-button.alignleft {
  margin-right: 20px !important;
  float: left;
  margin-top: 9px;
}
.wp-block-button__link {
  background-color: #32373c;
  border: none;
  border-radius: 28px;
  box-shadow: none;
  color: inherit;
  cursor: pointer;
  display: inline-block;
  font-size: 18px;
  margin: 0;
  padding: 12px 24px;
  text-align: center;
  text-decoration: none;
  overflow-wrap: break-word;
}
.wp-block-button {
  color: #fff;
  margin-bottom: 1.5em;
}
.wp-block-button.is-style-outline {
  color: #32373c;
}
.main-menu ul li:hover .submenu ul {
  display: none;
}
.main-menu ul li .submenu > li:hover ul.submenu {
  visibility: visible;
  opacity: 1;
  display: block;
  margin-top: -11px;
  margin-left: 10px;
}
@media (min-width: 992px) {
  .hamburger-menu.hamburger-menu-03 {
    display: none;
  }
}
.nice-select.open .list {
  width: 100%;
}
.scroll-target i {
  color: white;
}
p.logged-in-as {
  margin-bottom: 20px;
}
.comments-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
}
.contact-form-area.post-form-area .comment-respond {
  padding: 40px 40px 40px 40px;
  border: 2px solid #e4e4e4;
}
.widget.widget_recent_entries ul li {
  border-bottom: 1px dashed #cccccc;
  margin-bottom: 10px;
  padding-bottom: 10px;
}
.widget.widget_recent_entries ul li:last-child {
  border-bottom: 0px dashed #cccccc;
  margin-bottom: 0px;
  padding-bottom: 0px;
}
.main-menu ul li ul.submenu li {
  margin-right: 0px;
}
.post-form-area .input-message textarea {
  color: #2c3b4c;
}
.post-form-area .input-message textarea::placeholder {
  color: #2c3b4c;
}
.wp-block-button__link {
  background-color: #32373c;
  border: none;
  border-radius: 28px;
  box-shadow: none;
  color: inherit;
  cursor: pointer;
  display: inline-block;
  font-size: 18px;
  margin: 0;
  padding: 12px 24px;
  text-align: center;
  text-decoration: none;
  overflow-wrap: break-word;
}
.is-style-outline .wp-block-button__link {
  background-color: transparent;
  border: 2px solid;
}
.wp-block-button {
  color: #fff;
  margin-bottom: 1.5em;
}
.is-style-outline {
  color: #2c3b4c;
}
.alignleft {
  float: left;
}
.wp-block-cover .wp-block-cover-text {
  color: #fff !important;
}
.wp-block-cover {
  margin-bottom: 20px !important;
}
.wp-block-cover.has-left-content .wp-block-cover-text {
  margin: 0 auto;
}
.post-content ul.disc + blockquote {
  font-size: 15px !important;
  text-align: left;
  padding: 40px !important;
  display: block;
  position: relative;
  border: 2px solid var(--clr-theme-color);
  overflow: hidden;
  margin: 45px 0;
  color: #fff;
  background: var(--clr-theme-color) !important;
  border-radius: 10px;
}
.post-content ul.disc + blockquote::before {
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  position: absolute;
  content: "\22";
  right: 40px;
  bottom: 40px;
  font-size: 75px;
  opacity: 1;
  line-height: 1;
  color: #fff;
}
blockquote {
  background: #2c3b4c !important;
  border-left: 5px solid #2c3b4c;
  margin-bottom: 10px;
  padding: 15px !important;
  font-size: 15px !important;
  margin-top: 0;
  text-align: left;
  color: #fff;
}
blockquote p {
  color: #fff;
}
ul.wp-block-gallery,
.wt-post-meta ul {
  padding-left: 0px !important;
}
.wp-block-video figcaption {
  margin-top: 0.5em;
  margin-bottom: 1em;
  color: #2c3b4c;
  text-align: center;
  font-weight: 600;
}
iframe {
  max-width: 100%;
}
.wp-block-embed figcaption {
  margin-top: 0.5em;
  margin-bottom: 1em;
  color: #2c3b4c;
  text-align: center;
  font-size: 15px;
  font-weight: 600;
}
.wp-block-archives-dropdown select {
  margin-bottom: 20px;
  width: 100%;
}
.wp-block-categories-dropdown select {
  margin-bottom: 20px;
  width: 100%;
}
.page-title-area .page-title-inner {
  max-width: 100%;
}
.pagination li a {
  border: 0;
  background: 0;
  padding: 0;
  border-radius: 0;
  border: 2px solid #e4e4e4;
  border-radius: 50%;
  text-align: center;
  line-height: 48px;
  color: #2c3b4c;
  font-size: 18px;
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  width: 51px;
  height: 51px;
  display: inline-block;
}
.pagination li span {
  background: #2c3b4c;
  padding: 0;
  border-radius: 0;
  border: 2px solid #e4e4e4;
  border-radius: 50%;
  text-align: center;
  line-height: 48px;
  color: white;
  font-size: 18px;
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  width: 51px;
  height: 51px;
  display: inline-block;
}
.page-links .post-page-numbers .page-number {
  background: 0;
  padding: 0;
  border-radius: 0;
  border: 2px solid #e4e4e4;
  border-radius: 50%;
  text-align: center;
  line-height: 48px;
  color: #2c3b4c;
  font-size: 18px;
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  width: 51px;
  height: 51px;
  display: inline-block;
  margin-right: 5px;
}
.page-links .post-page-numbers .page-number:hover {
  z-index: 2;
  color: white;
  text-decoration: none;
  background-color: #dd9933;
  border-color: #dd9933;
}
.page-links .post-page-numbers.current .page-number {
  background: #dd9933;
  padding: 0;
  border-radius: 0;
  border: 2px solid #dd9933;
  border-radius: 50%;
  text-align: center;
  line-height: 48px;
  color: white;
  font-size: 18px;
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  width: 51px;
  height: 51px;
  display: inline-block;
}
table {
  border: 1px solid #2c3b4c !important;
  padding: 5px !important;
  margin-bottom: 22px;
  margin-top: 13px;
  width: 100%;
}
table * {
  border: 1px solid #2c3b4c !important;
  padding: 10px !important;
  color: #2c3b4c !important;
}
table td {
  padding: 10px !important;
}
table a {
  border: none !important;
  padding: 0px !important;
  font-weight: 700;
}
table a:hover {
  color: #dd9933 !important;
}
blockquote p strong code {
  color: #fff;
}
.aligncenter {
  text-align: center;
}
.alignright {
  float: right;
}
.nils_tm_copyright .copyright_inner {
  text-align: center;
}
.nils_tm_copyright {
  padding: 20px 0px 10px 0px;
}
.nils_tm_copyright .bottom {
  display: block;
}
.widget_search > form {
  position: relative;
  display: inline-block;
  width: 100%;
}
.nils_tm_page_wrapper .blog_list > ul > li .details {
  padding-top: 0px;
}
.nils_tm_page_wrapper .blog_list > ul > li .text {
  margin-bottom: 25px;
}
.nils_tm_page_wrapper .blog_list > ul > li .metabox .info span {
  padding-left: 20px;
  margin-right: 0px;
  border-radius: 5px;
}
.nils_tm_page_wrapper .blog_list > ul > li .image {
  position: relative;
  margin-bottom: 20px;
}
@media (max-width: 1400px) {
  .nils_tm_page_wrapper .blog_list > ul > li .metabox .title a {
    font-size: 36px;
  }
}
.nils_tm_page_wrapper .blog_list > ul > li .metabox .info span:before {
  display: none;
}
.nils_tm_page_wrapper .blog_list > ul > li .image img {
  opacity: 1;
  width: auto;
  min-width: auto;
  margin: 0 auto;
  text-align: center;
  display: block;
}
.nils_tm_comments {
  margin-top: 40px;
  margin-bottom: 0px;
}
p.logged-in-as a {
  color: #2c3b4c;
}
.wp-block-image .alignright {
  margin-top: 8px;
}
.wp-block-image .alignleft {
  margin-top: 8px;
}
.nils_tm_comment_filed {
  margin-top: 40px;
}
.alignleft {
  margin-right: 20px;
}
blockquote p strong em a {
  color: #fff;
}
blockquote p strong em a:hover {
  color: #fff;
}
.wp-block-pullquote.is-style-solid-color blockquote cite {
  text-transform: none;
  font-style: normal;
  color: #fff;
}
.nils_tm_comments .list ul li {
  margin: 0px 0px 0px 0px;
  width: 100%;
  float: left;
}

.nils_tm_comments .list > ul > li {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin: 0px 0px 0px 0px;
  margin-bottom: 30px;
}
.nils_tm_comments .list ul.children > li {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin: 0px 0px 0px 0px;
  margin-bottom: 30px;
}
.nils_tm_comments .list ul li .text {
  width: 100%;
  float: left;
  border-bottom: 0px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 0px;
}
::marker {
  display: none;
}
.has-text-color strong {
  color: #2c3b4c;
}
.blog_single_details ol,
.blog_single_details ul {
  color: #2c3b4c;
  padding-inline-start: 40px;
}
.nils_tm_all_wrap .wp-block-file a:first-child {
  color: #2c3b4c;
}
figure {
  margin-inline-start: 0px;
  margin-inline-end: 0px;
}
blockquote p strong + em + a {
  color: #fff;
}
blockquote p a code {
  color: #fff;
}
.blog_single_details dl a {
  color: #2c3b4c;
}
.blog_single_details p a {
  color: #2c3b4c;
}
.blog_single_details img.aligncenter,
.blog_single_details img.aligncenter {
  max-width: 100% !important;
  height: auto !important;
  margin: 0 auto;
  margin-bottom: 10px !important;
  display: block;
}
.blog_single_details .alignleft,
.blog_single_details .alignleft {
  margin-right: 20px !important;
  margin-top: 6px !important;
  max-width: 100% !important;
  height: auto !important;
  margin: 0 auto;
  margin-bottom: 10px !important;
}
.blog_single_details .alignright,
.blog_single_details .alignright {
  margin-left: 20px !important;
  margin-top: 6px !important;
  max-width: 100% !important;
  height: auto !important;
  margin: 0 auto;
  margin-bottom: 10px !important;
}
div.wp-caption.alignnone,
div.wp-caption.aligncenter {
  width: 100% !important;
}
.aligncenter {
  text-align: center;
  display: block;
  margin: 0 auto;
}
.wp-caption p.wp-caption-text,
.blog-detail .wp-caption-text a {
  text-align: center;
  font-weight: 600;
  line-height: 28px;
  font-size: 15px !important;
  margin-top: 10px;
}
.nils_tm_comments .list > ul {
  padding-inline-start: 0px;
}
.blog_single_details p strong em a {
  color: #fff;
}
pre {
  overflow: auto;
}
input[type="submit"] {
  height: 39px;
}
.nils_tm_comments .list ul li .list_inner.no_avatar {
  padding-left: 0px;
}
.page-id-146 .section-padding.blog.post-page p {
  margin-bottom: 20px;
  line-height: 28px;
  letter-spacing: 1px;
}
@media (min-width: 768px) {
  a.comment-reply-link {
    position: absolute;
    right: 0px;
    top: 0px;
  }
}
@media (max-width: 991px) {
  .comment .wrap a.comment-reply-link {
    position: relative;
    margin-top: 10px;
  }
}
a.comment-reply-link {
  color: #2c3b4c;
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
  transition: 0.3s;
  line-height: 1;
  border: 1px solid #2c3b4c;
  padding: 10px 15px;
  border-radius: 5px;
  margin-top: -10px;
  position: inherit;
}
a.comment-reply-link:hover {
  color: #fff;
  border: 1px solid #2c3b4c;
  background: #2c3b4c;
}

.list_inner {
  overflow: hidden;
  position: relative;
}

.nils_tm_header {
  top: 32px;
}
@media (max-width: 991px) {
  .nils_tm_mobile_menu .mobile_menu_inner {
    margin-top: 46px;
    border-bottom: 0px solid rgba(0, 0, 0, 0.1);
  }
}
@media (max-width: 768px) {
  .nils_tm_mobile_menu .mobile_menu_inner {
    margin-top: 60px;
    border-bottom: 0px solid rgba(0, 0, 0, 0.1);
  }
}
.nils_tm_header .sub-menu li a {
  line-height: 28px;
}
.nils_tm_header .sub-menu ul {
  top: 5px;
}
.nilsbrown-footer-section h5 {
  font-family: "Syne", sans-serif;
  font-size: 20px;
  font-weight: 700;
  color: #18191d;
  text-align: center;
}
.blog-post-categorydate-wrapper a {
  text-transform: capitalize;
}
.blog-sidebar .widget ul li a {
  color: #2c3b4c;
  text-transform: capitalize;
}
.widget_block.widget_search label.wp-block-search__label {
  display: none;
}
.widget_block .wp-block-search .wp-block-search__inside-wrapper {
  position: relative;
}
.widget_block .wp-block-search .wp-block-search__input {
  width: 100%;
  padding: 10px;
  border: 0;
  background: #fff;
  margin-bottom: 0;
  border-radius: 0px;
}
.widget_block
  .wp-block-search
  .wp-block-search__inside-wrapper
  .wp-block-search__button {
  position: absolute;
  right: 0;
  top: 0;
  background-color: transparent;
  color: #18191d;
  border: 0;
  padding: 14px;
  cursor: pointer;
}
.blog .post-cont a.cs-btn-one.btn-sm {
  padding: 10px 20px;
  background-color: #2c3b4c;
  border: none;
  color: #fff;
  float: left;
  margin-bottom: 0px;
  font-size: 16px;
  line-height: 1.5em;
  margin-top: 15px;
  border: 1px solid #2c3b4c;
}
.blog.section-padding .item .post-cont {
  background: #fff;
  padding: 30px;
  margin-bottom: 30px;
  overflow: hidden;
}
.blog .post-cont a.cs-btn-one.btn-sm:hover {
  background-color: #fff;
  color: #2c3b4c;
  border: 1px solid #2c3b4c;
}
body.dark .widget_block .wp-block-group .wp-block-group__inner-container h2 {
  color: #fff;
}
body.dark
  .widget.item.widget_block.widget_search
  .wp-block-search__button-outside.wp-block-search {
  display: inline-block;
}
.widget_block .wp-block-group .wp-block-group__inner-container h2 {
  padding-bottom: 5px;
  border-bottom: 0px solid rgba(0, 0, 0, 0.04);
  font-size: 24px;
  line-height: 1.75em;
  margin-bottom: 0px;
  color: #18191d;
}
.widget_block
  .wp-block-group
  .wp-block-group__inner-container
  .wp-block-latest-posts__list.wp-block-latest-posts
  li {
  margin-bottom: 20px;
  color: #868a9b;
  font-size: 16px;
  line-height: 1.5em;
  border-bottom: 1px dashed #cccccc;
  margin-bottom: 10px;
  padding-bottom: 10px;
  line-height: 40px;
}
.widget_block
  .wp-block-group
  .wp-block-group__inner-container
  .wp-block-latest-posts__list.wp-block-latest-posts
  li:last-child {
  margin-bottom: 0px;
  padding-bottom: 0px;
  border-bottom: 0px dashed #cccccc;
}
.widget_block
  .wp-block-group
  .wp-block-group__inner-container
  .wp-block-latest-comments
  .wp-block-latest-comments__comment {
  color: #868a9b;
  font-size: 16px;
  line-height: 1.5em;
  border-bottom: 1px dashed #cccccc;
  margin-bottom: 0px;
  padding-bottom: 10px;
  line-height: 40px;
}
.widget_block
  .wp-block-group
  .wp-block-group__inner-container
  .wp-block-latest-comments
  .wp-block-latest-comments__comment:last-child {
  margin-bottom: 0px;
  padding-bottom: 0px;
  border-bottom: 0px dashed #cccccc;
}
.widget_block
  .wp-block-group
  .wp-block-group__inner-container
  .wp-block-latest-comments {
  padding-left: 0px;
}
ul.children {
  margin-left: 20px;
}
.nilsbrown-footer-section {
  background: #fff;
  padding: 30px 0 20px 0;
  width: 100%;
}
.nilsbrown-footer-section h5 {
  font-family: "Syne", sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #18191d;
  text-align: center;
}
.blog-sidebar .widget ul li {
  margin-bottom: 0px;
  color: #2c3b4c;
  font-size: 16px;
  line-height: 40px;
}
.blog-sidebar .widget-title h5 {
  border-bottom: 0px solid rgba(0, 0, 0, 0.04);
}
.widget.widget_archive #archives-dropdown--1 {
  margin-top: 10px;
}
.blog-sidebar .widget-title h5 {
  margin-bottom: 0px;
}
.blog-sidebar .widget-title {
  margin-bottom: 0px;
}
.widget.widget_categories#monster-widget-placeholder-5 form {
  margin-top: 0px;
}
.widget.widget_rss ul li {
  border-bottom: 1px dashed #cccccc;
  margin-bottom: 10px;
  padding-bottom: 10px;
}
.widget.widget_rss ul li:last-child {
  border-bottom: 0px dashed #cccccc;
  margin-bottom: 00px;
  padding-bottom: 0px;
}
.blog.section-padding .item .post-img img {
  position: relative;
  overflow: hidden;
  width: auto;
  margin: 0 auto;
  display: block;
  text-align: center;
}
.blog-comment-section {
  clear: both;
}
.wp-block-pullquote p {
  color: #fff;
}
.wp-block-pullquote cite {
  color: #fff;
}
.wp-block-gallery.alignleft.extraclass {
  margin-right: 20px;
}
figure figcaption {
  font-weight: 700;
  text-align: center;
  margin-top: 13px !important;
}
.section-padding.blog.post-page .wp-block-image.size-full img {
  max-width: 100%;
  height: auto;
  width: auto;
}
.blog-sidebar .widget .calendar_wrap .wp-calendar-nav {
  display: none;
}
.blog-comment-section h3.comment-reply-title {
  font-size: 36px;
}
.section-padding.blog.post-page img.size-thumbnail {
  max-width: 160px !important;
}
.section-padding.blog.post-page .wp-caption-text,
.section-padding.blog.post-page .wp-caption-text a {
  text-align: center;
  font-weight: 700 !important;
  font-size: 15px !important;
  margin-top: 15px;
  margin-bottom: 20px !important;
}

.wp-block-calendar tbody td#today {
  background: #2c3b4c;
  color: #fff !important;
}
.wp-block-calendar table caption {
  font-size: 17px;
  text-align: center;
  font-weight: 500;
  caption-side: top;
  color: #fff !important;
  background: #2c3b4c;
}
.has-dates .wp-block-latest-comments__comment,
.has-excerpts .wp-block-latest-comments__comment {
  line-height: 1.5;
  font-size: 20px;
  font-weight: 700;
}
.wp-block-latest-comments li {
  border-bottom: 1px dotted #9999;
  padding-bottom: 0px;
  padding-top: 16px;
  margin-bottom: 5px;
  line-height: 23px;
}
.has-avatars .wp-block-latest-comments__comment {
  min-height: 2.25em;
  list-style: none;
}
.wp-block-search__inside-wrapper {
  position: relative;
  margin: 0px 0px;
}
.wp-block-search__inside-wrapper .wp-block-search__input {
  width: 100%;
  padding: 10px;
  margin-bottom: 0;
  border-radius: 0px;
}
.wp-block-search__inside-wrapper .wp-block-search__button {
  position: absolute;
  right: 0;
  top: 0;
  background-color: transparent;
  color: #18191d;
  border: 0;
  padding: 10px;
  cursor: pointer;
}
ul.wp-block-rss.wp-block-rss {
  box-sizing: border-box;
  padding-left: 30px;
}
.wp-block-group.has-background .wp-block-group__inner-container {
  padding: 20px;
  margin-bottom: 20px;
}
.alignleft {
  float: left;
  margin-right: 20px;
  margin-top: 8px;
}
.alignright {
  float: right;
  margin-left: 25px;
  margin-top: 8px;
}
img.thumbnail {
  width: auto;
  height: auto;
  text-align: center;
  display: block;
  margin: 0 auto;
  margin-bottom: 30px;
}
.post-password-form input[type="submit"] {
  background: #000;
  margin-left: -5px;
  height: 42px;
  border: 2px solid #000;
  padding: 0px 15px;
  color: #fff;
  font-size: 15px;
  text-transform: uppercase;
  top: 0px;
  width: auto;
  display: inline-block;
  border-radius: 0px;
}
.post-password-form input[type="password"] {
  height: 43px;
  border: 1px solid #000;
  padding-left: 15px;
  margin-top: 0px;
  width: auto;
  display: inline-block;
  border-radius: 0px;
}
.blog-comment-section h3 span {
  font-size: 14px;
  color: #2c3b4c;
  font-weight: 700;
  margin-left: 0px;
  clear: both;
  display: block;
}

.blog-comment-section .blog-post-comment-wrap .blog-post-user-content {
  display: block;
}
.blog-comment-section
  .blog-post-comment-wrap
  .blog-post-user-content
  .comment-reply-link {
  position: absolute;
  top: 10px;
  right: 0;
}
.blog-comment-section .blog-post-comment-wrap .blog-post-user-content {
  position: relative;
  overflow: hidden;
}
.blog-comment-section .blog-post-comment-wrap {
  display: block;
  border-bottom: 1px solid #e8e9ef;
  margin-bottom: 30px;
  padding-bottom: 30px;
}
.blog-comment-section .blog-post-comment-wrap p:last-child {
  margin-bottom: 0px;
}
.blog-comment-section .blog-post-comment-wrap .blog-post-user-comment {
  display: block;
  float: left;
}
.blog-post-user-content h2 {
  font-size: 32px;
}
.blog-comment-section h3 {
  text-transform: capitalize;
}
@media (max-width: 991px) {
  .blog-comment-section .blog-post-comment-wrap .blog-post-user-comment {
    display: block;
    float: none;
    margin-bottom: 20px;
  }
  .blog-comment-section
    .blog-post-comment-wrap
    .blog-post-user-content
    .comment-reply-link {
    position: initial;
    top: 10px;
    right: 0;
    margin-top: 8px;
  }
}
span.required-field-message {
  display: none;
}
.wp-container-2.wp-block-gallery-1.wp-block-gallery.columns-3
  .blocks-gallery-caption {
  margin-bottom: 20px;
  margin-top: 0px !important;
}
.wp-container-2.wp-block-gallery-1.wp-block-gallery.columns-3
  .blocks-gallery-caption
  strong {
  font-weight: 700;
}
@media (max-width: 600px) {
  .nilsbrown-header.scrolled {
    top: 0px;
  }
  ul.children {
    padding-left: 0px;
    clear: both;
  }
}
@media (min-width: 600px) {
  .nilsbrown-header.scrolled {
    top: 32px;
  }
}
ul.sub-menu.text-left {
  display: none;
}
.nilsbrown-menu-inner ul li.dropdown:hover ul.sub-menu.text-left {
  display: block;
}
.nilsbrown-menu-inner
  ul
  li.dropdown:hover
  ul.sub-menu.text-left
  ul.sub-menu.text-left {
  display: none;
}
.nilsbrown-menu-inner
  ul
  li.dropdown:hover
  ul.sub-menu.text-left
  li.dropdown:hover
  ul.sub-menu.text-left {
  display: block;
}
.section-padding.blog.post-page #post-1725 h2 {
  font-size: 32px;
}
.error .item {
  margin-bottom: 30px;
  text-align: center;
}
.error .item h1 {
  font-size: 200px;
}
.error .item p {
  margin-bottom: 30px;
}
.error .item a {
  margin: 5px !important;
  padding: 10px 24px;
  background-color: #000000;
  border: none;
  color: #fff !important;
  margin-bottom: 20px;
  color: #868a9b;
  font-size: 18px;
  margin-top: 51px;
  line-height: 1.5em !important;
  text-align: center;
  border: 1px #2c3b4c solid;
}
.error .item a:hover {
  color: #2c3b4c !important;
  background-color: #fff;
}
.search .search-custom .search input {
  width: 100%;
  padding: 10px;
  border: 0;
  background: #f4f5f7;
  margin-bottom: 0;
  border-radius: 0px;
}
.blog-post-user-comment img {
  width: 85px;
}
.blog-post-user-content p > img {
  margin: 10px 0px;
}
select {
  -webkit-appearance: none;
  background: url(../acens/images/arrow.png) no-repeat right;
  background-position-x: 96%;
  background-color: #fff;
  image-rendering: auto;
  background-size: 20px;
  image-rendering: crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  padding-left: 12px;
  width: 100%;
  padding: 0px 15px;
  font-size: 16px;
  line-height: 28px;
  border-radius: 5px;
  height: 45px;
  border: 1px solid;
  color: #2c3b4c !important;
  margin-top: 20px;
}
.wp-block-button .wp-block-button__link {
  border: 2px solid #111 !important;
}
.wp-block-button .wp-block-button__link {
  border-radius: 4px !important;
  font-size: 15px;
}
.wp-block-button .wp-block-button__link {
  font-weight: 500 !important;
  background: #111 !important;
}
.wp-block-button .wp-block-button__link:hover {
  color: #fff;
}
.wp-block-button.is-style-outline .wp-block-button__link {
  background: white !important;
  color: #111 !important;
}
dl dd {
  margin-left: 20px;
}
.search button .ti-search {
  color: #fff;
}
.wp-block-file * + .wp-block-file__button:hover {
  color: #fff;
}
.search .search-custom form .s-input-home {
  width: 100%;
  height: 50px;
  padding-left: 10px;
  max-width: 100%;
  border: 1px solid #999;
  background: #fff;
  border-radius: 5px;
}
.blog .item .wrap:first-child {
  padding: 0px 0px 30px 0px;
}
.blog .category {
  text-transform: capitalize;
}
.wp-block-search__inside-wrapper .wp-block-search__input:focus-visible {
  outline: -webkit-focus-ring-color auto 0px;
}
select:focus-visible {
  outline: -webkit-focus-ring-color auto 0px;
}
.widget ul li a.rsswidget {
  font-size: 18px;
}
.widget.item.widget_search form {
  display: flex;
}
.widget.item.widget_search form button {
  position: absolute;
  right: 0;
}
.comment .wrap .text h5 span {
  font-size: 14px;
  color: #2c3b4c;
  font-weight: 700;
  margin-left: 0px;
  display: block;
}
.comment .wrap .text {
  position: relative;
  margin-left: 20px;
}
.blog .comment .img img {
  margin-right: 0px;
}
.footer .copyrights.full-width {
  text-align: center;
  justify-content: end;
}
.footer .copyrights.full-width p {
  color: #fff;
  justify-content: end;
}

@media (max-width: 768px) {
  position: relative;
  .footer .bottom,
  .footer .copyrights.full-width,
  .footer .copyrights.full-width p {
    text-align: center;
    z-index: 1;
  }

  .footer .copyrights.full-width,
  .footer .copyrights.full-width p {
    position: relative;
    justify-content: center;
    z-index: 1;
  }
}

blockquote:before {
  display: none;
}
p.has-small-font-size + p.has-large-font-size + p {
  line-height: 50px !important;
  font-size: 46px !important;
}
.blog .blog-post-page ul {
  list-style-type: disc;
}
.blog .blog-post-page img {
  border-radius: 0px;
}
.blog .blog-post-page .wp-block-latest-posts.wp-block-latest-posts__list {
  list-style: none;
  padding-left: 40px !important;
}
.blog .mb-30.thumb {
  margin: 0 auto;
  display: block;
  margin-bottom: 30px;
  width: auto;
}
.blog.section-padding.post-page .date-post {
  margin-bottom: 10px;
}
.blog .comment .wrap {
  display: block;
}
.blog .comment .wrap .img.img-grayscale {
  float: left;
}
.blog .comment .wrap .text {
  margin-left: 120px;
}
.blog .comment .wrap .text h5.title-cm {
  display: inline-block;
  margin-bottom: 5px;
  text-transform: capitalize;
  line-height: 1.5em;
  font-weight: 700;
  font-size: 20px;
}
.blog .comment .wrap .text p img {
  margin: 10px 0px;
  width: auto;
  border-radius: 0px;
}
.blog .comment .wrap .text ul {
  list-style-type: disc;
}
.blog .comment .wrap .text ul ul {
  list-style-type: circle;
}
.blog .comment .wrap .text ul ul ul {
  list-style-type: square;
}
@media (max-width: 600px) {
  .blog .comment .wrap .img.img-grayscale {
    float: none;
  }
  .blog .comment .wrap .text {
    margin-left: 0px;
    margin-top: 10px;
  }
}
.blog .comment .wrap.no-avatar .text {
  margin-left: 0px;
}
.navbar {
  top: 32px;
}
.nav-scroll {
  top: -100px;
}
@media (max-width: 782px) {
  .navbar {
    top: 46px;
  }
  .nav-scroll {
    top: -54px;
  }
}
@media (max-width: 600px) {
  .navbar {
    top: 0px; /* hide navbar on mobile 46px*/
  }
  .nav-scroll {
    top: -100px;
  }
}
/* ============================
   Responsive Desktop Dropdown (991px - 1365px)
   Smaller screens get more compact dropdowns
   ============================ */
@media screen and (max-width: 1365px) and (min-width: 991px) {
  .navbar .dropdown-menu {
    min-width: 200px;
    padding: 15px; /* Slightly smaller padding for compact screens */
  }
  .navbar .dropdown-menu .dropdown-menu {
    min-width: 200px;
  }

  /* Adjust item padding for smaller dropdowns */
  .navbar .dropdown-menu .dropdown-item {
    padding: 10px 15px;
  }

  .navbar .dropdown-menu .dropdown-item:hover,
  .navbar .dropdown-menu .dropdown-item:focus {
    padding-left: 20px; /* Smaller hover indent */
  }
}
.nav-scroll .navbar-nav.ms-auto > .nav-item > .nav-link:hover {
  color: #dd9933;
}
.error .text-center h2 {
  font-size: 160px;
}
.error a.cs-btn-one.btn-md.btn-primary-color {
  display: inline-block;
  color: #2c3b4c;
  margin: 45px !important;
  padding: 11px 32px;
  background-color: transparent;
  border: none;
  border: 2px solid #dd9933;
  border-radius: 30px;
  line-height: 1.5em;
  font-weight: 500;
  font-size: 20px !important;
}
.error a.cs-btn-one.btn-md.btn-primary-color:hover {
  background-color: #dd9933;
  color: #fff;
}
/* ============================
   Desktop Dropdown Menu Styling (≥992px)
   ============================ */
@media screen and (min-width: 992px) {
  /* Disable Bootstrap dropdown JavaScript behavior for desktop */
  .navbar .dropdown-toggle {
    pointer-events: auto !important;
  }

  /* Ensure parent links are clickable */
  .navbar .dropdown > .nav-link {
    pointer-events: auto !important;
    cursor: pointer !important;
  }

  /* Main dropdown menu container */
  .navbar .dropdown-menu {
    display: block;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    min-width: 240px;
    padding: 20px;
    border: 0;
    background-color: #ffffff;
    transition: all 0.3s ease;
    border-radius: 10px;
    box-shadow: 0 16px 50px rgba(27, 27, 27, 0.1);
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    pointer-events: auto;
  }

  /* Show dropdown on hover - using both dropdown container and parent link hover */
  .navbar .dropdown:hover > .dropdown-menu,
  .navbar .dropdown-menu:hover,
  .navbar .dropdown-menu.show-hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  /* Ensure smooth transition when moving from parent to dropdown */
  .navbar .dropdown {
    position: relative;
  }

  /* Create hover bridge to prevent dropdown from disappearing */
  .navbar .dropdown::before {
    content: "";
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px;
    background: transparent;
    z-index: 999;
    pointer-events: none;
  }

  /* Ensure dropdown menu has proper pointer events */
  .navbar .dropdown-menu {
    pointer-events: auto;
  }

  /* Prevent dropdown from interfering with parent link clicks */
  .navbar .dropdown > .nav-link {
    position: relative;
    z-index: 1001;
  }

  /* Dropdown items styling */
  .navbar .dropdown-menu .dropdown-item {
    padding: 12px 20px;
    color: #2c3b4c;
    font-size: clamp(
      1rem,
      calc(0.875rem + 0.3vw),
      1.125rem
    ); /* Responsive equivalent of 16px */
    font-weight: 400;
    border-bottom: none;
    background-color: transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-radius: 5px;
    margin-bottom: 2px;
  }

  /* Dropdown item hover effects */
  .navbar .dropdown-menu .dropdown-item:hover,
  .navbar .dropdown-menu .dropdown-item:focus {
    color: var(--clr-theme-color);
    background-color: rgba(171, 123, 42, 0.05);
    padding-left: 25px;
    transform: translateX(0);
  }

  /* Dropdown item icons */
  .navbar .dropdown-menu .dropdown-item i {
    padding: 0 0 0 8px;
    font-size: clamp(
      0.8rem,
      calc(0.85rem + 0.05vw),
      1rem
    ); /* Responsive equivalent of 14px */
    float: right;
    color: var(--clr-theme-color);
    transition: all 0.3s ease;
  }

  /* Sub-dropdown menu positioning */
  .navbar .dropdown-menu .dropdown-menu {
    left: calc(100% + 5px);
    top: -20px;
    right: auto;
    min-width: 240px;
    transform: translateY(0);
    margin: 0;
  }

  /* Ensure proper spacing for list items */
  .navbar .dropdown-menu li {
    margin-bottom: 0;
    padding-left: 0;
    border-bottom: none;
  }

  /* Remove hover padding animation for desktop - use background instead */
  .navbar .dropdown-menu li:hover {
    padding-left: 0;
  }

  /* Dark theme dropdown styling */
  body.dark .navbar .dropdown-menu {
    background-color: #202020;
    box-shadow: 0 16px 50px rgba(0, 0, 0, 0.3);
  }

  body.dark .navbar .dropdown-menu .dropdown-item {
    color: #ffffff;
  }

  body.dark .navbar .dropdown-menu .dropdown-item:hover,
  body.dark .navbar .dropdown-menu .dropdown-item:focus {
    color: var(--clr-theme-color);
    background-color: rgba(171, 123, 42, 0.1);
  }

  body.dark .navbar .dropdown-menu .dropdown-item i {
    color: #ffffff;
  }

  body.dark .navbar .dropdown-menu .dropdown-item:hover i,
  body.dark .navbar .dropdown-menu .dropdown-item:focus i {
    color: var(--clr-theme-color);
  }
}

  /* Fallback: Disable Bootstrap dropdown toggle behavior on desktop */
  .navbar .dropdown-toggle[data-bs-toggle="dropdown"] {
    pointer-events: auto !important;
  }

  /* Ensure dropdown items are accessible */
  .navbar .dropdown-menu .dropdown-item {
    pointer-events: auto;
  }

  /* Mobile compatibility - restore Bootstrap behavior */
  @media screen and (max-width: 991px) {
    .navbar .dropdown-toggle {
      pointer-events: auto !important;
    }

    .navbar .dropdown::before {
      display: none;
    }

    .navbar .dropdown-menu {
      position: static;
      transform: none;
      opacity: 1;
      visibility: visible;
    }
  }
}

/* ============================
   JavaScript Enhancement for Desktop Dropdown Functionality
   Note: The JavaScript file handles Bootstrap dropdown behavior override
   ============================ */
.blog .item .wrap > h4 {
  font-size: 28px;
}
.comment-respond .comment-reply-title small a {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  line-height: 1.75em;
  -webkit-transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease;
  border: none;
  border-radius: 80px;
  background-color: #dd9933;
  color: #fff;
  line-height: 20px;
  text-align: center;
  padding: 12px 20px;
  height: auto;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  margin-top: 0px;
  display: inline-block;
  font-size: 14px;
  float: right;
}
.blog-post-page p img {
  width: auto;
}
.blog .blog-post-page .post-password-form p + p {
  display: flex;
}
.blog-sidebar .item .text h5 {
  font-size: 22px;
}
.blog .item .wrap a.cs-btn-one.btn-sm {
  display: inline-block;
  color: #2c3b4c;
  padding: 11px 32px;
  background-color: transparent;
  border: none;
  border: 2px solid #dd9933;
  border-radius: 30px;
  line-height: 1.5em;
  font-weight: 500;
  margin-top: 10px;
}
.blog .item .wrap a.cs-btn-one.btn-sm:hover {
  background-color: #dd9933;
  color: #fff;
}
.blog .comment .text h5 {
  font-size: 20px;
  text-transform: capitalize;
}
figure.wp-block-gallery figcaption.blocks-gallery-caption {
  margin-top: 0px !important;
  margin-bottom: 15px;
}
ul {
  list-style-type: disc;
}
ul.post-categories {
  list-style-type: none;
}
.blog.post-page img {
  border-radius: 5px;
  width: 100%;
}
.blog.post-page .wp-block-media-text .wp-block-media-text__media img {
  width: 100%;
}
.blog.post-page form p + p {
  display: flex;
}
.blog.post-page .post-content {
  position: relative;
}
.featured-post {
  display: block;
  color: white;
  padding: 3px 20px 3px 20px;
  font-size: 14px;
  font-weight: 600;
  float: right;
  margin-right: 2px;
  border-radius: 2px;
  letter-spacing: 1px;
  margin-right: 40px;
  margin: 0px !important;
  background-color: #1a1a1a;
  border: none;
  margin-bottom: 20px;
  font-size: 16px;
  top: 0px;
  position: absolute;
  right: 0px;
}
.blog .item .wrap {
  position: relative;
}
blockquote p {
  max-width: 100%;
}
.blog-sidebar .item .recent a {
  display: block !important;
  margin-left: 105px;
  float: none;
  line-height: 28px;
}
.blog-sidebar .item .recent li {
  display: block;
  overflow: hidden;
  margin-bottom: 15px;
}
body.dark .work-single .s-list > li > :last-child {
  margin-bottom: 0;
  color: #fff;
}
body.dark ul.disc li {
  margin-bottom: 10px;
  text-align: left;
  color: #ccc;
}
body.dark .blog .comment .wrap {
  display: block;
}
body.dark .blog .comment .wrap .text {
  margin-left: 130px;
}
body.dark .blog .comment h5 span {
  margin-left: 0px;
}
.blog .comment .wrap.comment_demo h5 span {
  margin-left: 15px;
  display: inline-block;
}
.comment .wrap.comment_demo a.comment-reply-link {
  position: relative;
  top: 8px;
  right: 0;
}
body.dark .blog-home .page-numbers {
  margin-right: 10px;
}
.section-padding.not-found h2 {
  font-size: clamp(6rem, 11.25vw + 2rem, 11.25rem);
  line-height: 1;
  font-family: "Poppins", sans-serif;
  color: var(--clr-theme-color);
  margin-bottom: 0.9375rem;
  font-weight: 900;
}
body.dark .blog.section-padding.post-page {
  padding-bottom: 0px;
}
input.wpcf7-form-control.wpcf7-submit {
  height: unset;
}

/* Blog post page: normal links only, buttons excluded */
.blog.post-page .post-content a:not(.btn):not(.btn-1):not(.btn-2):not(.btn-3):not(.quotations):not([role="button"]) {
    color: var(--clr-theme-color);
}

/* Blog post page: normal HOVER */
.blog.post-page .post-content a:not(.btn):not(.btn-1):not(.btn-2):not(.btn-3):not(.quotations):not([role="button"]):hover {
color: #2c3b4c;
}

/* -------MY CUSTOM CSS----------- */

/* Let's Talk Section Enhancements */
.lets-talk .btn-1,
.lets-talk .btn-5 {
  margin: 0 10px;
  display: inline-block;
}

.lets-talk .btn-1:hover,
.lets-talk .btn-5:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Responsive spacing for Let's Talk buttons */
@media (max-width: 768px) {
  .lets-talk .btn-1,
  .lets-talk .btn-5 {
    display: block;
    margin: 10px auto;
    width: fit-content;
  }
}

.footer .top .whatsapp {
  font-size: clamp(1.125rem, 2.5vw, 1.375rem);
  color: var(--clr-theme-color);
  font-weight: 700;
  display: block;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  font-family: "Poppins", sans-serif;
}

.footer .top .whatsapp a {
  color: var(--clr-theme-color);
}

.footer .top .whatsapp a:hover .footer .top .whatsapp:hover {
  color: var(--clr-theme-color);
}

/* -------MY CUSTOM CSS FROM CUSTOMIZES----------- */

/* Classic Div */
/* .alignnone {
    margin-top: 40px !important;
    margin-bottom: 40px !important;
} */

/* FOOTER MENU COLOR FIX */
#nav_menu-3 {
  color: #ba7b22;
  font-size: clamp(0.875rem, 1.6vw, 1rem);
}

#nav_menu-3 ul li a:hover {
  color: #ba7b22;
}

#nav_menu-3 .menu li {
  color: #ba7b22; /* Bullet color */
}

#nav_menu-3 .menu li a {
  color: #b7b9ba;
  text-decoration: none;
}

/* --FOOTER -Menu Categories */
#categories-10 {
  color: #ba7b22;
  font-size: clamp(0.875rem, 1.6vw, 1rem);
}

#categories-10 ul li {
  color: #ba7b22;
}

#categories-10 ul li a {
  color: #b7b9ba;
}

#categories-3 ul li a:hover {
  color: #ba7b22;
}

/* ----BLOG HOME TITLE------ */
.blog-home .title {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  font-size: clamp(1rem, 1.5vw + 0.4rem, 1.25rem);
  margin-bottom: 5px;
  text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.7),
    -1px -1px 1px rgba(255, 255, 255, 0.2);
  padding: 0 1rem;
}

/* RECAPTCHA REPOSITION AND STYLE */
.grecaptcha-badge {
  left: 25px !important;
  right: auto !important;
  bottom: 30px !important;
  position: fixed !important;
  width: 60px !important;
  height: 60px !important;
  overflow: hidden !important;
  z-index: 1000 !important;
  border-radius: 50% !important;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); /* optional: for a subtle elevation */
  display: none !important;
}

/* Hover (desktop only) - expand but keep some roundness */
@media (min-width: 769px) {
  .grecaptcha-badge:hover {
    width: 256px !important;
    border-radius: 50px !important;
  }
}

/* On mobile: smaller and still round */
@media (max-width: 768px) {
  .grecaptcha-badge {
    /* transform: scale(0.8);
    transform-origin: left bottom; */
    width: 60px !important;
    height: 60px !important;
  }

  /* hide if not form */
  .grecaptcha-badge {
    display: none !important;
  }
}

/* TABLE FIX */
/* Clean up span or inline elements inside table cells */
.blog-home table td span,
.blog-home table td * {
  border: none !important;
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
  background: transparent !important;
  display: inline !important;
}

.blog-home span {
  display: inline-block;
  padding: 2px;
}

.blog-home table td {
  vertical-align: top !important;
  padding: 8px !important;
}

/* side by side POST CTA button */
.btn-group {
  display: flex;
  gap: 10px;
  margin-top: 1rem !important;
  flex-wrap: wrap;
}

/* work content */
@media (max-width: 768px) {
  .section-padding.work-single .row > * {
    margin-top: 3rem;
  }
}

/* SERVICES CONTENT */

/* Headings */
.trds-services-section h2 {
  font-size: clamp(1.75rem, 3vw, 2.5rem);
  font-weight: 900 !important;
  line-height: 1.3;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.trds-services-section h3 {
  font-size: clamp(1rem, 1.5vw, 1.3rem);
  font-weight: 800;
  line-height: 1.2;
  color: var(--clr-theme-color);
  margin-top: 2rem;
  margin-bottom: 0.75rem;
}

/* Paragraphs */
.trds-services-section p {
  line-height: 1.6;
}

/* Image */
.trds-services-section .gallery-img img {
  display: block;
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
}

/* Optional override class */
.trds-services-section .mb-30 {
  margin-bottom: 1.875rem !important;
}

/* Footer */
.trds-services-section footer {
  margin-top: 2rem;
}

.trds-services-section footer h3 {
  font-size: clamp(1.3rem, 2.5vw, 1.7rem);
  margin-bottom: 1rem;
  color: inherit;
}

.trds-services-section footer p {
  margin-bottom: 2rem;
}

.trds-services-section .marg-top2 {
  margin-top: 1rem;
}

/* Paragraph  Group Styles */
@media (max-width: 600px) {
  .trds-services-section {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 2rem;
  }

  .services-single .col-lg-4.sidebar-side {
    flex-basis: 100% !important; /* make column full width */
    max-width: 100% !important;
    padding: 0 !important; /* remove side padding */
  }

  .services-single .sidebar {
    width: 100% !important; /* make sidebar fill its container */
    padding: 0 !important;
  }
}

/* PORTFOLIO CONTENT */

/* Container */
.trds-portfolio-section {
  padding: 0;
}

.section-padding.work-single {
  padding: clamp(40px, 10vw, 120px) 0 0 0 !important; /* top padding */
}

/* Headings */
.trds-portfolio-section h2 {
  font-size: clamp(1.75rem, 3vw, 2.5rem);
  font-weight: 900;
  line-height: 1.3;
  /*   margin-top: 1.5rem; */
  margin-bottom: 1rem;
}

.trds-portfolio-section h3 {
  font-size: clamp(1rem, 1.5vw, 1.3rem);
  font-weight: 800;
  line-height: 1.2;
  color: var(--clr-theme-color);
  margin-top: 2rem;
  margin-bottom: 0.75rem;
}

/* Paragraphs */
.trds-portfolio-section p {
  line-height: 1.6;
}

/* List Style */
/* .trds-portfolio-section ul.disc {
  list-style-type: disc;
  padding-left: 1.5rem;
} */

.trds-portfolio-section .mt-30 {
  margin-top: 1.875rem;
}

/* Image */
/* .trds-portfolio-section .gallery-img img {
  margin: 1.5rem 0;
}
 */

/* Footer */
.trds-portfolio-section footer {
  margin-top: 2rem;
}

.trds-portfolio-section footer h3 {
  font-size: clamp(1.3rem, 2.5vw, 1.7rem);
  margin-bottom: 1rem;
  color: inherit;
}

.trds-portfolio-section footer p {
  margin-bottom: 2rem;
}

.trds-portfolio-section hr {
  margin-top: 2rem !important; /* or whatever space you prefer */
}

/* Button Group (reuses .btn-3 styles) */
.trds-portfolio-section .btn-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* 1️⃣ Portfolio Section: normal links only, buttons excluded */
.trds-portfolio-section a:not(.btn):not(.btn-1):not(.btn-2):not(.btn-3):not(.quotations):not([role="button"]) {
  color: var(--clr-theme-color)
}

/* 1️⃣ Portfolio Section: normal links only, buttons excluded - HOVER */
.trds-portfolio-section a:not(.btn):not(.btn-1):not(.btn-2):not(.btn-3):not(.quotations):not([role="button"]):hover {
  color: #2c3b4c;
}

/* Responsive Padding */
@media (max-width: 600px) {
  .trds-portfolio-section {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 2rem;
  }
}

/*
 * [Services Section Enhancement]
 * Ensure h3 and h2 in .services .item .wrap .con have consistent font-size, color, and margin-bottom.
 * Also update hover and active states for better contrast and accessibility.
 * This is a child theme override for service card headings.
 */
.services .item .wrap .con h3,
.services .item .wrap .con h2 {
  font-size: clamp(
    1.5rem,
    calc(1.4rem + 0.2vw),
    1.6875rem
  ); /* Consistent with parent */
  color: #2c3b4c;
  margin-bottom: 15px;
}
.services .item:hover .wrap .con h3,
.services .item:hover .wrap .con h2 {
  color: #fff;
}
.services .item.active .wrap .con h3,
.services .item.active .wrap .con h2 {
  color: #fff;
}

/* BLOG PAGE */

/* Did you know section */
.did-you-know {
  background-color: #f8f9fa;
  border-left: 4px solid #ab7b2a;
  padding: 15px;
  margin: 20px 0;
  border-radius: 0 4px 4px 0;
}

.statistics {
  background-color: #f8e2bc56;
  border: 1px solid #f7e3c2;
  padding: 15px 20px;
  margin: 20px 0;
  border-radius: 4px;
}

.statistics ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.source {
  font-size: 0.9em;
  color: #666;
  font-style: italic;
  margin-top: 30px;
}

/* Formid Form custom CSS */

/* Webkit specific (Chrome, Safari) */
@supports (-webkit-appearance: none) {
  #trds_quote_formid select option:hover {
    background: #ba7b22 !important;
    color: #fff !important;
  }
}

/* Firefox specific */
@-moz-document url-prefix() {
  #trds_quote_formid select option:hover {
    background-color: #ba7b22 !important;
    color: #fff !important;
  }
}

/* ----------- */

/* Blog posts CTA Button Styling - Fixed for Compact Layout */
body.single-post .wp-block-buttons {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 1rem;
  width: auto !important;
  max-width: fit-content !important;
  margin-top: 2.5rem !important;
  margin-bottom: 2rem !important;
}

/* Style the actual button link */
body.single-post .wp-block-buttons .wp-block-button > .wp-block-button__link {
  display: inline-block !important;
  width: auto !important;
  max-width: 100% !important;
  height: auto !important;
  padding: clamp(0.8rem, 1vw + 0.6rem, 1.2rem)
    clamp(1.5rem, 2vw + 0.8rem, 2.5rem) !important;
  border: 1px solid #9a742b !important;
  border-radius: 80px !important;
  background-color: #ab7b2a !important; /* Forced base color */
  transition: border-color 400ms ease, color 400ms ease,
    background-color 400ms ease !important;
  color: #fff !important;
  line-height: 1.2 !important;
  text-align: center !important;
  font-family: "Poppins", sans-serif !important;
  font-weight: 900 !important;
  font-size: clamp(0.9rem, calc(1vw + 0.3rem), 1.25rem) !important;
  text-decoration: none !important;
  cursor: pointer !important;
  white-space: nowrap !important;
}

/* Retain existing hover styles */
body.single-post
  .wp-block-buttons
  .wp-block-button
  > .wp-block-button__link:hover {
  background-color: #313b4b !important;
  color: #fff !important;
  border-color: #343b4a !important;
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  body.single-post .wp-block-buttons {
    justify-content: center !important;
    max-width: 100% !important;
  }

  body.single-post .wp-block-buttons .wp-block-button > .wp-block-button__link {
    font-size: 1rem !important;
    padding: 0.75rem 1.25rem !important;
    white-space: normal !important;
    text-align: center !important;
  }
}

/* ----------- */

/* ============================
   Floating Chat Button (Default)
   ============================ */
:not(#\20):not(#\20):not(#\20).buttonizer-group-0-0-1 {
  right: 30px !important;
  bottom: 50px !important;
  z-index: 101 !important;
}

/* ============================
     Mobile View Only (Max Width: 768px)
     ============================ */
@media (max-width: 768px) {
  /* Position the main button on the left side */

  :not(#\20):not(#\20):not(#\20).buttonizer-group-0-0-1 {
    left: 30px !important;
    right: auto !important;
    bottom: 30px !important;
    z-index: 100 !important;
    width: 50px !important;
    height: 50px !important;
    transform: scaleX(-1) !important;
  }

  /* Base style for all expanded buttons */
  .buttonizer-group-0-0-1 .buttonizer-button:not(.buttonizer-head) {
  }

  /* Chat boxes */
  ._widget_10ma8_1 {
    display: absolute !important;
    left: 30px !important;
    right: auto !important;
    bottom: 30px !important;
    z-index: 101 !important;
  }

  /* Flip all buttonizer tooltip text back to normal */
  .buttonizer-label {
    transform: scaleX(-1) !important;
    -webkit-transform: scaleX(-1) !important;
  }
}

/* ============================
  HIDE Labels on Mobile View Only (Max Width: 768px)
   ============================ */
   /* Config via settings */

@media (max-width: 768px) {
  /* Hide Buttonizer Label on Mobile */
  .buttonizer-group-0-0-1
    .buttonizer-button:not(.buttonizer-head)
    .buttonizer-tooltip {
    display: none !important;
  }
}

/* ----------- */

/*
 * CSS Layering Fix for Buttonizer Plugin vs. Elementor Video Widget
 *
 * This CSS ensures that the Buttonizer button appears on top of the
 * Elementor video widget by adjusting the z-index.
*/

.elementor-widget-video {
  position: relative;
  z-index: 1;
}

#buttonizer-button-0-0-6-label {
  z-index: 1000 !important; /* A high value to ensure it's on top */
}

.buttonizer-group-0-0-1 {
  z-index: 1000 !important;
}

/*
 * Progress Wrap Mobile Fix
 *
 * This CSS adjusts the position of the 'progress-wrap' element on mobile
 * devices to prevent it from overlapping with the footer.
*/

/* @media (max-width: 767px) {
  .progress-wrap {
    margin-bottom: 50px;
  }
} */

/* ============================
   Progress Circle Repositioning
   ============================ */

/*
 * Progress Circle Desktop Positioning
 *
 * Repositions the progress-circle element to the left side on desktop view
 * (similar to mobile buttonizer positioning) while maintaining current mobile behavior.
 * Uses the same positioning strategy as the buttonizer main button.
*/

/* Desktop view - reposition to left */
@media (min-width: 769px) {
  .progress-wrap {
    left: 30px !important;
    right: auto !important;
    bottom: 30px !important; /* Consistent with mobile buttonizer */
    z-index: 99 !important; /* Below buttonizer to avoid conflicts */
  }
}

/* Mobile view - maintain current positioning */
@media (max-width: 768px) {
  .progress-wrap {
    /* Current mobile positioning is already correct */
    /* No changes needed - inherits base styles */
    /* Keeping this comment block for documentation purposes */
  }
}
